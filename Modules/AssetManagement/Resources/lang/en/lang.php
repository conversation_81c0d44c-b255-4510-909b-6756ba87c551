<?php

return [
    'asset_management' => 'Asset Management',
    'view_asset' => 'View Asset',
    'asset_category' => 'Asset category',
    'asset_categories' => 'Asset categories',
    'manage_asset_categories' => 'Manage asset categories',
    'assets' => 'Assets',
    'asset' => 'Asset',
    'all_assets' => 'All assets',
    'add_asset' => 'Add asset',
    'edit_asset' => 'Edit asset',
    'asset_code' => 'Asset code',
    'series_model' => 'Series/Model',
    'warranty_period' => 'Warranty period',
    'depreciation' => 'Depreciation',
    'is_allocatable' => 'Is allocatable?',
    'warranty_period_in_month' => 'Warranty period in <i><b>month</b></i>',
    'image_replace_help_text' => 'Previous image (if exists) will be replaced',
    'asset_allocation' => 'Asset Allocation',
    'allocation_code' => 'Allocation code',
    'allocated_at' => 'Allocated at',
    'reason' => 'Reason',
    'create' => 'Create',
    'edit' => 'Edit',
    'asset_allocated' => 'Asset allocated',
    'allocate_asset' => 'Allocate asset',
    'allocated_qty' => 'Allocated quantity',
    'asset_settings' => 'Asset settings',
    'asset_code_prefix' => 'Asset code prefix',
    'allocation_code_prefix' => 'Allocation code prefix',
    'revoke_code_prefix' => 'Revoke code prefix',
    'revoke' => 'Revoke',
    'revoke_code' => 'Revoke code',
    'revoked_at' => 'Revoked at',
    'revoke_asset' => 'Revoke <code>:allocation_code</code>',
    'revoked_asset' => 'Asset revoked',
    'revoked_for' => 'Revoked for',
    'revoked_by' => 'Revoked by',
    'allocate_to' => 'Allocate to',
    'allocated_to' => 'Allocated to',
    'allocated_by' => 'Allocated by',
    'revoked_qty' => 'Revoked qty',
    'additional_notes' => 'Additional Note',
    'end_date' => 'End Date',
    'add_more' => 'Add More',
    'additional_cost' => 'Additional Cost',
    'in_warranty' => 'In Warranty',
    'days_left' => 'Days left',
    'not_in_warranty' => 'Not in warranty',
    'serial_no' => 'Serial Number',
    'model_no' => 'Model Name/Number',
    'purchase_type' => 'Purchase Type',
    'owned' => 'Owned',
    'rented' => 'Rented',
    'leased' => 'Leased',
    'allocatable_tooltip' => 'If allocatable, asset can be assigned to any other user',
    'total_assets_allocated' => 'Total assets allocated',
    'total_assets' => 'Total Assets',
    'expired_or_expiring_in_one_month' => 'Assets expired or expiring in one month',
    'expiring_on' => 'Expiring on',
    'assets_by_category' => 'Assets by category',
    'warranty_status' => 'Warranty status',
    'allocate_from' => 'Allocated from',
    'allocated_upto' => 'Allocated upto',
    'asset_name' => 'Asset name',
    'warranty_months' => 'Warranty Months',
    'total_assets_allocated_to_you' => 'Assets Allocated to you',
    'send_to_maintenance' => 'Send asset for maintenance',
    'asset_maintenance' => 'Asset maintenance',
    'new' => 'New',
    'in_progress' => 'In progress',
    'high' => 'High',
    'medium' => 'Medium',
    'low' => 'Low',
    'details' => 'Details',
    'attachments' => 'Attachments',
    'asset_maintenance_prefix' => 'Asset maintenance prefix',
    'last_maintenance_date' => 'Last maintenance date',
    'maintenance_id' => 'Maintenance id',
    'view_all_maintenance' => 'View all maintenance',
    'view_own_maintenance' => 'View own maintenance',
    'n_in_maintenance' => ':n in maintenance',
    'datetime' => 'Date time',
    'unassigned' => 'Unassigned',
    'download_attachments' => 'Download attachments',
    'asset_send_for_maintenance_notification' => 'Asset send for maintenance notification',
    'enable_email' => 'Enable email',
    'asset_assigned_for_maintenance_notification' => 'Asset assigned for maintenance notification',
    'recipients' => 'Recipients',
    'asset_assigned_for_maintenance' => 'Asset :asset_code assigned for maintenance',
    'asset_sent_for_maintenance' => 'Asset :asset_code sent for maintenance',
    'send_for_maintenance_details' => 'Send for maintenance details',
    'maintenance_note' => 'Maintenance note',
    'edit_asset' => 'Edit asset',
    'delete_asset' => 'Delete asset',
];
