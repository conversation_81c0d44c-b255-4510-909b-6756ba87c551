<?php

namespace Modules\Receipt\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Menu;

class DataController extends Controller
{
    /**
     * Return permissions for Receipt module
     *
     * @return array
     */
    public function user_permissions()
    {
        return [
            [
                'value' => 'receipt.view',
                'label' => __('receipt::lang.view_receipt'),
                'default' => false
            ],
            [
                'value' => 'receipt.create',
                'label' => __('receipt::lang.add_receipt'),
                'default' => false
            ],
            [
                'value' => 'receipt.update',
                'label' => __('receipt::lang.edit_receipt'),
                'default' => false
            ],
            [
                'value' => 'receipt.delete',
                'label' => __('receipt::lang.delete_receipt'),
                'default' => false
            ]
        ];
    }

    /**
     * Modify admin menu for Receipt module
     *
     * @return void
     */
    public function modifyAdminMenu()
    {
        Menu::modify('admin-sidebar-menu', function ($menu) {
            $menu->dropdown(__('receipt::lang.receipts'), function ($sub) {
                $sub->url(
                    action('Modules\Receipt\Http\Controllers\ReceiptController@index'),
                    __('receipt::lang.all_receipts'),
                    ['icon' => 'fa fa-list', 'active' => request()->segment(1) == 'receipt']
                );
                $sub->url(
                    action('Modules\Receipt\Http\Controllers\ReceiptController@create'),
                    __('receipt::lang.add_receipt'),
                    ['icon' => 'fa fa-plus', 'active' => request()->segment(2) == 'create']
                );
                $sub->url(
                    action('TaxonomyController@index') . '?type=receipt_category',
                    __('receipt::lang.receipt_categories'),
                    ['icon' => 'fa fa-tags', 'active' => request()->get('type') == 'receipt_category']
                );
            }, ['icon' => 'fa fa-money']);
        });
    }

    /**
     * Add taxonomies for Receipt module
     *
     * @return array
     */
    public function addTaxonomies()
    {
        return [
            'receipt_category' => [
                'heading' => __('receipt::lang.receipt_categories'),
                'sub_heading' => __('receipt::lang.manage_receipt_categories'),
                'enable_taxonomy_code' => true,
                'taxonomy_code_label' => __('receipt::lang.category_code'),
                'taxonomy_code_help_text' => __('receipt::lang.category_code_help'),
                'enable_sub_taxonomy' => true
            ]
        ];
    }

    /**
     * Parse notifications for Receipt module
     *
     * @param $notification
     * @return array|null
     */
    public function parse_notification($notification)
    {
        if ($notification->type == 'Modules\Receipt\Notifications\ReceiptCreatedNotification') {
            $notification_data = [
                'msg' => __('receipt::lang.receipt_created_notification'),
                'icon_class' => 'fa fa-money',
                'link' => action('Modules\Receipt\Http\Controllers\ReceiptController@show', $notification->data['receipt_id']),
                'read_at' => $notification->read_at,
                'created_at' => $notification->created_at->diffForHumans()
            ];
            
            return $notification_data;
        }

        return null;
    }
}
