<?php

namespace Modules\Receipt\Http\Controllers;

use App\Account;
use App\AccountTransaction;
use App\BusinessLocation;
use App\Contact;
use App\Events\TransactionPaymentAdded;
use App\TaxRate;
use App\Transaction;
use App\User;
use App\Utils\CashRegisterUtil;
use App\Utils\ModuleUtil;
use App\Utils\TransactionUtil;
use DB;
use Excel;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;
use Modules\Receipt\Events\ReceiptCreatedOrModified;

class ReceiptController extends Controller
{
    /**
     * All Utils instance.
     */
    protected $transactionUtil;
    protected $moduleUtil;
    protected $cashRegisterUtil;

    /**
     * Constructor
     *
     * @param TransactionUtil $transactionUtil
     * @return void
     */
    public function __construct(TransactionUtil $transactionUtil, ModuleUtil $moduleUtil, CashRegisterUtil $cashRegisterUtil)
    {
        $this->transactionUtil = $transactionUtil;
        $this->moduleUtil = $moduleUtil;
        $this->cashRegisterUtil = $cashRegisterUtil;
    }

    /**
     * Display a listing of the resource.
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (!auth()->user()->can('receipt.view')) {
            abort(403, 'Unauthorized action.');
        }

        $business_id = request()->session()->get('user.business_id');

        if (request()->ajax()) {
            $receipts = Transaction::leftJoin('contacts', 'transactions.contact_id', '=', 'contacts.id')
                ->leftJoin('tax_rates', 'transactions.tax_id', '=', 'tax_rates.id')
                ->leftJoin('business_locations as bl', 'transactions.location_id', '=', 'bl.id')
                ->leftJoin('users as u', 'transactions.created_by', '=', 'u.id')
                ->where('transactions.business_id', $business_id)
                ->where('transactions.type', 'receipt')
                ->select(
                    'transactions.id',
                    'transactions.transaction_date',
                    'transactions.ref_no',
                    'transactions.final_total',
                    'contacts.name as contact_name',
                    'transactions.payment_status',
                    'transactions.additional_notes',
                    'transactions.receipt_for',
                    'transactions.receipt_from',
                    'bl.name as location_name',
                    'u.first_name',
                    'u.last_name'
                );

            $permitted_locations = auth()->user()->permitted_locations();
            if ($permitted_locations != 'all') {
                $receipts->whereIn('transactions.location_id', $permitted_locations);
            }

            return Datatables::of($receipts)
                ->addColumn('action', function ($row) {
                    $html = '<div class="btn-group">
                        <button type="button" class="btn btn-info dropdown-toggle btn-xs"
                            data-toggle="dropdown" aria-expanded="false">' .
                        __("messages.actions") .
                        '<span class="caret"></span><span class="sr-only">Toggle Dropdown
                        </span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-right" role="menu">';

                    if (auth()->user()->can('receipt.view')) {
                        $html .= '<li><a href="' . action('Modules\Receipt\Http\Controllers\ReceiptController@show', [$row->id]) . '"><i class="fas fa-eye" aria-hidden="true"></i> ' . __("messages.view") . '</a></li>';
                    }
                    if (auth()->user()->can('receipt.update')) {
                        $html .= '<li><a href="' . action('Modules\Receipt\Http\Controllers\ReceiptController@edit', [$row->id]) . '"><i class="glyphicon glyphicon-edit"></i> ' . __("messages.edit") . '</a></li>';
                    }
                    if (auth()->user()->can('receipt.delete')) {
                        $html .= '<li><a data-href="' . action('Modules\Receipt\Http\Controllers\ReceiptController@destroy', [$row->id]) . '" class="delete_receipt_button"><i class="glyphicon glyphicon-trash"></i> ' . __("messages.delete") . '</a></li>';
                    }

                    $html .= '</ul></div>';

                    return $html;
                })
                ->editColumn('final_total', function ($row) {
                    return '<span class="display_currency" data-currency_symbol="true">' . $row->final_total . '</span>';
                })
                ->editColumn('transaction_date', function ($row) {
                    return $this->transactionUtil->format_date($row->transaction_date, true);
                })
                ->editColumn('payment_status', function ($row) {
                    return __('lang_v1.' . $row->payment_status);
                })
                ->rawColumns(['final_total', 'action'])
                ->make(true);
        }

        $business_locations = BusinessLocation::forDropdown($business_id, false, false);
        $receipt_categories = [];

        return view('receipt::index')
            ->with(compact('business_locations', 'receipt_categories'));
    }

    /**
     * Show the form for creating a new resource.
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        if (!auth()->user()->can('receipt.create')) {
            abort(403, 'Unauthorized action.');
        }

        $business_id = request()->session()->get('user.business_id');

        //Check if subscribed or not
        if (!$this->moduleUtil->isSubscribed($business_id)) {
            return $this->moduleUtil->expiredResponse();
        }

        $business_locations = BusinessLocation::forDropdown($business_id, false, false);
        $users = User::forDropdown($business_id, false, false, false);

        //Get all contacts
        $contacts = Contact::contactDropdown($business_id, false, false);

        $taxes = TaxRate::forBusinessDropdown($business_id, true, true);

        $receipt_categories = [];

        return view('receipt::create')
            ->with(compact('business_locations', 'users', 'contacts', 'taxes', 'receipt_categories'));
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (!auth()->user()->can('receipt.create')) {
            abort(403, 'Unauthorized action.');
        }

        try {
            $business_id = $request->session()->get('user.business_id');

            //Check if subscribed or not
            if (!$this->moduleUtil->isSubscribed($business_id)) {
                return $this->moduleUtil->expiredResponse(action('Modules\Receipt\Http\Controllers\ReceiptController@index'));
            }

            //Validate document size
            $request->validate([
                'document' => 'file|max:' . (config('constants.document_size_limit') / 1000),
            ]);

            $user_id = $request->session()->get('user.id');

            DB::beginTransaction();

            $receipt = $this->transactionUtil->createReceipt($request, $business_id, $user_id);

            if (request()->ajax()) {
                $payments = !empty($request->input('payment')) ? $request->input('payment') : [];
                $this->cashRegisterUtil->addSellPayments($receipt, $payments);
            }

            //update payment status
            $this->transactionUtil->updatePaymentStatus($receipt->id, $receipt->final_total);

            $receipt = Transaction::find($receipt->id);

            //Upload document
            if ($request->hasFile('document')) {
                $this->transactionUtil->uploadFile($request, $receipt, 'document');
            }

            event(new ReceiptCreatedOrModified($receipt));

            DB::commit();

            $output = ['success' => true,
                'data' => $receipt,
                'msg' => __('receipt::lang.receipt_added_success')
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::emergency("File:" . $e->getFile() . "Line:" . $e->getLine() . "Message:" . $e->getMessage());

            $output = ['success' => false,
                'msg' => __('messages.something_went_wrong')
            ];
        }

        if ($request->input('submit_type') == 'submit_n_add_another') {
            return redirect()->action('Modules\Receipt\Http\Controllers\ReceiptController@create')->with('status', $output);
        } elseif ($request->input('submit_type') == 'save_n_add_another') {
            return redirect()->action('Modules\Receipt\Http\Controllers\ReceiptController@create')->with('status', $output);
        }

        return redirect('receipt')->with('status', $output);
    }

    /**
     * Display the specified resource.
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        if (!auth()->user()->can('receipt.view')) {
            abort(403, 'Unauthorized action.');
        }

        $business_id = request()->session()->get('user.business_id');
        $receipt = Transaction::where('business_id', $business_id)
            ->where('type', 'receipt')
            ->with(['contact', 'tax', 'location', 'payment_lines', 'payment_lines.method'])
            ->findOrFail($id);

        $payment_types = $this->transactionUtil->payment_types(null, true, $business_id);

        return view('receipt::show')
            ->with(compact('receipt', 'payment_types'));
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        if (!auth()->user()->can('receipt.update')) {
            abort(403, 'Unauthorized action.');
        }

        $business_id = request()->session()->get('user.business_id');

        //Check if subscribed or not
        if (!$this->moduleUtil->isSubscribed($business_id)) {
            return $this->moduleUtil->expiredResponse();
        }

        $receipt = Transaction::where('business_id', $business_id)
            ->where('type', 'receipt')
            ->with(['contact', 'tax', 'location'])
            ->findOrFail($id);

        $business_locations = BusinessLocation::forDropdown($business_id, false, false);
        $users = User::forDropdown($business_id, false, false, false);

        //Get all contacts
        $contacts = Contact::contactDropdown($business_id, false, false);

        $taxes = TaxRate::forBusinessDropdown($business_id, true, true);

        $receipt_categories = [];

        return view('receipt::edit')
            ->with(compact('receipt', 'business_locations', 'users', 'contacts', 'taxes', 'receipt_categories'));
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        if (!auth()->user()->can('receipt.update')) {
            abort(403, 'Unauthorized action.');
        }

        try {
            $business_id = $request->session()->get('user.business_id');

            //Validate document size
            $request->validate([
                'document' => 'file|max:' . (config('constants.document_size_limit') / 1000),
            ]);

            DB::beginTransaction();

            $receipt = Transaction::where('business_id', $business_id)
                ->where('type', 'receipt')
                ->findOrFail($id);

            $receipt = $this->transactionUtil->updateReceipt($receipt, $request);

            //Upload document
            if ($request->hasFile('document')) {
                $this->transactionUtil->uploadFile($request, $receipt, 'document');
            }

            event(new ReceiptCreatedOrModified($receipt));

            DB::commit();

            $output = ['success' => true,
                'msg' => __('receipt::lang.receipt_updated_success')
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::emergency("File:" . $e->getFile() . "Line:" . $e->getLine() . "Message:" . $e->getMessage());

            $output = ['success' => false,
                'msg' => __('messages.something_went_wrong')
            ];
        }

        return redirect('receipt')->with('status', $output);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        if (!auth()->user()->can('receipt.delete')) {
            abort(403, 'Unauthorized action.');
        }

        try {
            $business_id = request()->session()->get('user.business_id');

            DB::beginTransaction();

            $receipt = Transaction::where('business_id', $business_id)
                ->where('type', 'receipt')
                ->findOrFail($id);

            //Delete receipt payments
            $this->transactionUtil->deletePaymentLines($receipt->id);

            event(new ReceiptCreatedOrModified($receipt, true));

            $receipt->delete();

            DB::commit();

            $output = ['success' => true,
                'msg' => __('receipt::lang.receipt_deleted_success')
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::emergency("File:" . $e->getFile() . "Line:" . $e->getLine() . "Message:" . $e->getMessage());

            $output = ['success' => false,
                'msg' => __('messages.something_went_wrong')
            ];
        }

        return $output;
    }
}
