<?php

namespace Modules\Receipt\Listeners;

use App\AccountTransaction;
use App\Events\TransactionPaymentAdded;
use App\Events\TransactionPaymentDeleted;
use App\Events\TransactionPaymentUpdated;
use Modules\Receipt\Events\ReceiptCreatedOrModified;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class ReceiptTransactionListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  ReceiptCreatedOrModified  $event
     * @return void
     */
    public function handle(ReceiptCreatedOrModified $event)
    {
        $receipt = $event->receipt;
        $isDeleted = $event->isDeleted;

        if ($isDeleted) {
            // Delete related account transactions when receipt is deleted
            AccountTransaction::where('transaction_id', $receipt->id)
                ->where('sub_type', 'receipt')
                ->delete();
        } else {
            // Create or update account transactions for receipt
            $this->updateAccountTransactions($receipt);
        }
    }

    /**
     * Update account transactions for receipt
     *
     * @param  object  $receipt
     * @return void
     */
    private function updateAccountTransactions($receipt)
    {
        // Delete existing account transactions for this receipt
        AccountTransaction::where('transaction_id', $receipt->id)
            ->where('sub_type', 'receipt')
            ->delete();

        // Create new account transaction for receipt
        if ($receipt->final_total > 0) {
            $account_transaction_data = [
                'account_id' => 1, // Default cash account
                'type' => 'credit', // Receipt is credit to business
                'sub_type' => 'receipt',
                'amount' => $receipt->final_total,
                'transaction_id' => $receipt->id,
                'transaction_payment_id' => null,
                'operation_date' => $receipt->transaction_date,
                'created_by' => $receipt->created_by,
                'note' => $receipt->additional_notes,
            ];

            AccountTransaction::createAccountTransaction($account_transaction_data);
        }
    }

    /**
     * Handle payment added event for receipts
     *
     * @param  TransactionPaymentAdded  $event
     * @return void
     */
    public function handlePaymentAdded(TransactionPaymentAdded $event)
    {
        $payment = $event->payment;
        $transaction = $payment->transaction;

        if ($transaction->type == 'receipt') {
            // Create account transaction for receipt payment
            $account_transaction_data = [
                'account_id' => $payment->account_id ?? 1,
                'type' => 'credit', // Receipt payment is credit
                'sub_type' => 'receipt_payment',
                'amount' => $payment->amount,
                'transaction_id' => $transaction->id,
                'transaction_payment_id' => $payment->id,
                'operation_date' => $payment->paid_on,
                'created_by' => $transaction->created_by,
                'note' => $payment->note,
            ];

            AccountTransaction::createAccountTransaction($account_transaction_data);
        }
    }

    /**
     * Handle payment updated event for receipts
     *
     * @param  TransactionPaymentUpdated  $event
     * @return void
     */
    public function handlePaymentUpdated(TransactionPaymentUpdated $event)
    {
        $payment = $event->payment;
        $transaction = $payment->transaction;

        if ($transaction->type == 'receipt') {
            // Update account transaction for receipt payment
            AccountTransaction::where('transaction_payment_id', $payment->id)
                ->where('type', 'credit')
                ->where('sub_type', 'receipt_payment')
                ->update([
                    'account_id' => $payment->account_id ?? 1,
                    'amount' => $payment->amount,
                    'operation_date' => $payment->paid_on,
                    'note' => $payment->note,
                ]);
        }
    }

    /**
     * Handle payment deleted event for receipts
     *
     * @param  TransactionPaymentDeleted  $event
     * @return void
     */
    public function handlePaymentDeleted(TransactionPaymentDeleted $event)
    {
        $payment = $event->payment;
        $transaction = $payment->transaction;

        if ($transaction->type == 'receipt') {
            // Delete account transaction for receipt payment
            AccountTransaction::where('transaction_payment_id', $payment->id)
                ->where('type', 'credit')
                ->where('sub_type', 'receipt_payment')
                ->delete();
        }
    }
}
