# Receipt Module for UltimatePOS

## Tổng quan

Module Receipt (<PERSON>hu nhập) là một module mở rộng cho hệ thống UltimatePOS, cho phép quản lý các phiếu thu tiền một cách hiệu quả. Module này được xây dựng theo cấu trú<PERSON>-Module và tích hợp hoàn toàn với hệ thống UltimatePOS hiện có.

## Tính năng chính

### 1. Qu<PERSON>n lý Phiếu Thu (CRUD)
- **Tạo phiếu thu mới**: Tạo phiếu thu với đầy đủ thông tin
- **Xem danh sách phiếu thu**: Hiển thị tất cả phiếu thu với bộ lọc
- **Chỉnh sửa phiếu thu**: Cập nhật thông tin phiếu thu
- **<PERSON><PERSON><PERSON> phiếu thu**: <PERSON><PERSON><PERSON> phiếu thu và các giao dịch liên quan

### 2. <PERSON><PERSON><PERSON> hợ<PERSON>ệ thống
- **T<PERSON><PERSON> hợp với Account Transactions**: Tự động tạo giao dịch tài khoản
- **Tích hợp với Payment System**: Hỗ trợ nhiều phương thức thanh toán
- **Tích hợp với Permission System**: Quản lý quyền truy cập chi tiết
- **Tích hợp với Menu System**: Tự động thêm menu vào sidebar

### 3. Báo cáo và Thống kê
- **Báo cáo thu nhập**: Thống kê thu nhập theo thời gian
- **Báo cáo theo danh mục**: Phân tích thu nhập theo danh mục
- **Báo cáo theo địa điểm**: Thống kê thu nhập theo business location

## Cấu trúc Module

```
Modules/Receipt/
├── Config/
│   └── config.php                 # Cấu hình module
├── Database/
│   └── Migrations/               # Database migrations
├── Events/
│   └── ReceiptCreatedOrModified.php  # Events
├── Http/
│   └── Controllers/
│       ├── DataController.php    # Integration controller
│       └── ReceiptController.php # Main controller
├── Listeners/
│   └── ReceiptTransactionListener.php # Event listeners
├── Providers/
│   ├── EventServiceProvider.php  # Event service provider
│   ├── ReceiptServiceProvider.php # Main service provider
│   └── RouteServiceProvider.php  # Route service provider
├── Resources/
│   ├── lang/en/lang.php          # Language files
│   └── views/                    # Blade templates
└── Routes/
    └── web.php                   # Web routes
```

## Cài đặt

### 1. Yêu cầu hệ thống
- UltimatePOS đã được cài đặt
- PHP >= 7.4
- Laravel >= 8.0
- MySQL/MariaDB

### 2. Cài đặt Module
1. Copy thư mục `Modules/Receipt` vào thư mục `Modules` của UltimatePOS
2. Chạy migration:
   ```bash
   php artisan module:migrate Receipt
   ```
3. Clear cache:
   ```bash
   php artisan cache:clear
   php artisan config:clear
   ```

### 3. Cấu hình Permissions
Sau khi cài đặt, cần cấp quyền cho users:
- `receipt.view` - Xem phiếu thu
- `receipt.create` - Tạo phiếu thu
- `receipt.update` - Chỉnh sửa phiếu thu
- `receipt.delete` - Xóa phiếu thu

## Sử dụng

### 1. Truy cập Module
Sau khi cài đặt thành công, menu "Receipts" sẽ xuất hiện trong sidebar admin với các submenu:
- All Receipts
- Add Receipt
- Receipt Categories

### 2. Tạo Phiếu Thu
1. Vào menu "Receipts" > "Add Receipt"
2. Điền thông tin:
   - Business Location (bắt buộc)
   - Receipt Date (bắt buộc)
   - Total Amount (bắt buộc)
   - Receipt Category
   - Receipt For (người nhận)
   - Receipt From (nguồn thu)
   - Contact
   - Tax
   - Notes
   - Document attachment
3. Thêm payment information
4. Click "Save"

### 3. Quản lý Phiếu Thu
- **Xem danh sách**: Vào "All Receipts" để xem tất cả phiếu thu
- **Lọc dữ liệu**: Sử dụng các bộ lọc theo location, category, payment status, date range
- **Chỉnh sửa**: Click vào nút "Edit" trong danh sách
- **Xem chi tiết**: Click vào nút "View" để xem chi tiết phiếu thu
- **Xóa**: Click vào nút "Delete" để xóa phiếu thu

## API và Tích hợp

### 1. Events
Module phát ra các events sau:
- `ReceiptCreatedOrModified`: Khi phiếu thu được tạo hoặc sửa đổi

### 2. Database Schema
Module sử dụng bảng `transactions` hiện có với:
- `type = 'receipt'`
- Các cột bổ sung: `receipt_category_id`, `receipt_for`, `receipt_from`

### 3. Account Transactions
Mỗi phiếu thu sẽ tự động tạo:
- Account transaction với type = 'receipt'
- Payment transactions khi có thanh toán

## Tùy chỉnh

### 1. Thêm Fields
Để thêm fields mới cho phiếu thu:
1. Tạo migration để thêm cột vào bảng `transactions`
2. Cập nhật form trong views
3. Cập nhật controller để xử lý field mới

### 2. Thêm Validation Rules
Cập nhật validation rules trong `ReceiptController`:
```php
$request->validate([
    'field_name' => 'required|rule',
    // ...
]);
```

### 3. Tùy chỉnh Views
Các view files có thể được tùy chỉnh:
- `index.blade.php` - Danh sách phiếu thu
- `create.blade.php` - Form tạo phiếu thu
- `edit.blade.php` - Form chỉnh sửa
- `show.blade.php` - Chi tiết phiếu thu

## Troubleshooting

### 1. Module không xuất hiện trong menu
- Kiểm tra permissions của user
- Kiểm tra module đã được enable: `php artisan module:list`
- Clear cache: `php artisan cache:clear`

### 2. Lỗi migration
- Kiểm tra database connection
- Chạy lại migration: `php artisan module:migrate-refresh Receipt`

### 3. Lỗi permissions
- Kiểm tra permissions đã được tạo trong database
- Gán permissions cho role/user thông qua admin panel

## Hỗ trợ

Nếu gặp vấn đề khi sử dụng module, vui lòng:
1. Kiểm tra log files trong `storage/logs`
2. Kiểm tra browser console cho JavaScript errors
3. Đảm bảo tất cả requirements đã được đáp ứng

## Phiên bản

- **Version**: 1.0
- **Tương thích**: UltimatePOS 6.0+
- **Laravel**: 8.0+

## License

Module này được phát triển cho UltimatePOS và tuân theo license của UltimatePOS.
