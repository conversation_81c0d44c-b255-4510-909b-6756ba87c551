@extends('layouts.app')
@section('title', __('receipt::lang.receipt_details'))

@section('content')
  <!-- Main content -->
  <section class="content">
    <div class="row">
      <div class="col-xs-12">
        <h2 class="page-header">
          @lang('receipt::lang.receipt_details')
          <small class="pull-right"><b>@lang('messages.date'):</b> {{ @format_datetime($receipt->transaction_date) }}</small>
        </h2>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-4">
        <b>@lang('purchase.ref_no'):</b> #{{ $receipt->ref_no }}<br>
        <b>@lang('business.location'):</b> {{ $receipt->location->name }}<br>
        <b>@lang('sale.status'):</b> {{ ucfirst( $receipt->status ) }}<br>
        <b>@lang('sale.payment_status'):</b> {{ ucfirst( $receipt->payment_status ) }}<br>
        @if(!empty($receipt->receipt_category_id))
          <b>@lang('receipt::lang.receipt_category'):</b> {{ $receipt->receipt_category->name ?? '' }}<br>
        @endif
      </div>
      <div class="col-sm-4">
        @if(!empty($receipt->contact))
          <b>@lang('contact.contact'):</b> {{ $receipt->contact->name }}<br>
          @if(!empty($receipt->contact->supplier_business_name))
            <b>@lang('business.business'):</b> {{ $receipt->contact->supplier_business_name }}<br>
          @endif
        @endif
        @if(!empty($receipt->receipt_for))
          <b>@lang('receipt::lang.receipt_for'):</b> {{ $receipt->receipt_for }}<br>
        @endif
        @if(!empty($receipt->receipt_from))
          <b>@lang('receipt::lang.receipt_from'):</b> {{ $receipt->receipt_from }}<br>
        @endif
      </div>
      <div class="col-sm-4">
        @if(!empty($receipt->tax))
          <b>@lang('product.tax'):</b> {{ $receipt->tax->name }} ({{ $receipt->tax->amount }}%)<br>
        @endif
        <b>@lang('sale.total_amount'):</b> <span class="display_currency" data-currency_symbol="true">{{ $receipt->final_total }}</span><br>
        @if(!empty($receipt->document))
          <b>@lang('purchase.attach_document'):</b> 
          <a href="{{asset('uploads/documents/' . $receipt->document)}}" target="_blank">
            <i class="fa fa-external-link"></i> {{$receipt->document}}
          </a><br>
        @endif
      </div>
    </div>
    <br>
    
    @if(!empty($receipt->additional_notes))
    <div class="row">
      <div class="col-xs-12">
        <p><b>@lang('receipt::lang.receipt_notes'):</b></p>
        <p class="well well-sm no-shadow bg-gray" style="border-radius: 0px;">
         {{ $receipt->additional_notes }}
        </p>
      </div>
    </div>
    @endif

    <div class="row">
      <div class="col-xs-12">
        <div class="table-responsive">
          <table class="table bg-gray">
            <tr>
              <th>@lang('sale.total_before_tax'): </th>
              <td><span class="display_currency" data-currency_symbol="true">{{ $receipt->total_before_tax }}</span></td>
            </tr>
            <tr>
              <th>@lang('product.tax'): </th>
              <td><span class="display_currency" data-currency_symbol="true">{{ $receipt->tax_amount }}</span></td>
            </tr>
            <tr class="bg-gray">
              <th>@lang('sale.total_amount'): </th>
              <td><span class="display_currency" data-currency_symbol="true">{{ $receipt->final_total }}</span></td>
            </tr>
          </table>
        </div>
      </div>
    </div>

    @if(count($receipt->payment_lines) > 0)
    <div class="row">
      <div class="col-xs-12">
        <h3>@lang('purchase.payment_info'):</h3>
        <div class="table-responsive">
          <table class="table bg-gray">
            <tr class="bg-green">
              <th>#</th>
              <th>@lang('messages.date')</th>
              <th>@lang('purchase.payment_method')</th>
              <th>@lang('sale.amount')</th>
              <th>@lang('lang_v1.payment_note')</th>
              <th>@lang('messages.action')</th>
            </tr>
            @php 
              $total_paid = 0.00;
            @endphp
            @foreach($receipt->payment_lines as $payment_line)
              <tr>
                <td>{{ $loop->iteration }}</td>
                <td>{{ @format_datetime($payment_line->paid_on) }}</td>
                <td>
                  {{ $payment_types[$payment_line->method] ?? $payment_line->method }}
                  @if($payment_line->method == 'cheque')
                    <br>(@lang('lang_v1.cheque_no'): {{ $payment_line->cheque_number }})
                  @elseif($payment_line->method == 'card')
                    <br>(@lang('lang_v1.card_transaction_no'): {{ $payment_line->card_transaction_number }})
                  @elseif($payment_line->method == 'bank_transfer')
                    <br>(@lang('lang_v1.bank_account_no'): {{ $payment_line->bank_account_number }})
                  @elseif( in_array($payment_line->method, ['custom_pay_1', 'custom_pay_2', 'custom_pay_3']))
                    <br>(@lang('lang_v1.transaction_no'): {{ $payment_line->transaction_no }})
                  @endif
                </td>
                <td><span class="display_currency" data-currency_symbol="true">{{ $payment_line->amount }}</span></td>
                <td>{{ $payment_line->note }}</td>
                <td>
                  @can('receipt.update')
                    <button type="button" class="btn btn-primary btn-xs edit_payment" 
                      data-href="{{ action('TransactionPaymentController@edit', [$payment_line->id]) }}">
                      @lang('messages.edit')
                    </button>
                  @endcan
                  @can('receipt.delete')
                    <button type="button" class="btn btn-danger btn-xs delete_payment" 
                      data-href="{{ action('TransactionPaymentController@destroy', [$payment_line->id]) }}">
                      @lang('messages.delete')
                    </button>
                  @endcan
                </td>
              </tr>
              @php 
                $total_paid += $payment_line->amount;
              @endphp
            @endforeach
            <tr class="bg-gray">
              <td colspan="3"><strong>@lang('sale.total_paid'):</strong></td>
              <td><span class="display_currency" data-currency_symbol="true">{{ $total_paid }}</span></td>
              <td colspan="2"></td>
            </tr>

              <td colspan="2"></td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    @endif

    <div class="row">
      <div class="col-xs-12 text-center">
        @can('receipt.update')
          <a href="{{ action('Modules\Receipt\Http\Controllers\ReceiptController@edit', [$receipt->id]) }}" 
             class="btn btn-primary">
            <i class="glyphicon glyphicon-edit"></i> @lang('messages.edit')
          </a>
        @endcan
        
        @can('receipt.create')
          <button type="button" class="btn btn-success add_payment_modal" 
            data-href="{{ action('TransactionPaymentController@addPayment', [$receipt->id]) }}"
            data-container=".payment_modal">
            <i class="fa fa-plus"></i> @lang('purchase.add_payment')
          </button>
        @endcan

        <button type="button" class="btn btn-info" onclick="window.print();">
          <i class="fa fa-print"></i> @lang('messages.print')
        </button>
      </div>
    </div>
  </section>

  <div class="modal fade payment_modal" tabindex="-1" role="dialog" 
      aria-labelledby="gridSystemModalLabel">
  </div>

  <div class="modal fade edit_payment_modal" tabindex="-1" role="dialog" 
      aria-labelledby="gridSystemModalLabel">
  </div>
@stop

@section('javascript')
<script src="{{ asset('js/payment.js?v=' . $asset_v) }}"></script>
<script type="text/javascript">
  $(document).ready(function(){
    __currency_convert_recursively($('body'));
  });
</script>
@endsection
