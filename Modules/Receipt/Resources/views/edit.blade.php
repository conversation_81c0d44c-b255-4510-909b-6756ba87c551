@extends('layouts.app')
@section('title', __('receipt::lang.edit_receipt'))

@section('content')

<!-- Content Header (Page header) -->
<section class="content-header">
    <h1 class="tw-text-xl md:tw-text-3xl tw-font-bold tw-text-black">@lang('receipt::lang.edit_receipt')</h1>
</section>

<!-- Main content -->
<section class="content">
  {!! Form::open(['url' => action('Modules\Receipt\Http\Controllers\ReceiptController@update', [$receipt->id]), 'method' => 'PUT', 'id' => 'edit_receipt_form', 'files' => true ]) !!}
  <div class="box box-solid">
    <div class="box-body">
      <div class="row">
        <div class="col-sm-4">
          <div class="form-group">
            {!! Form::label('location_id', __('purchase.business_location').':*') !!}
            {!! Form::select('location_id', $business_locations, $receipt->location_id, ['class' => 'form-control select2', 'placeholder' => __('messages.please_select'), 'required']); !!}
          </div>
        </div>
        <div class="col-sm-4">
          <div class="form-group">
            {!! Form::label('receipt_category_id', __('receipt::lang.receipt_category').':') !!}
            {!! Form::select('receipt_category_id', $receipt_categories, $receipt->receipt_category_id, ['class' => 'form-control select2', 'placeholder' => __('messages.please_select')]); !!}
          </div>
        </div>
        <div class="col-sm-4">
          <div class="form-group">
            {!! Form::label('ref_no', __('purchase.ref_no').':') !!}
            {!! Form::text('ref_no', $receipt->ref_no, ['class' => 'form-control']); !!}
          </div>
        </div>
        <div class="col-sm-4">
          <div class="form-group">
            {!! Form::label('transaction_date', __('messages.date') . ':*') !!}
            <div class="input-group">
              <span class="input-group-addon">
                <i class="fa fa-calendar"></i>
              </span>
              {!! Form::text('transaction_date', @format_datetime($receipt->transaction_date), ['class' => 'form-control', 'readonly', 'required', 'id' => 'receipt_transaction_date']); !!}
            </div>
          </div>
        </div>
        <div class="col-sm-4">
          <div class="form-group">
            {!! Form::label('final_total', __('sale.total_amount') . ':*') !!}
            {!! Form::text('final_total', @num_format($receipt->final_total), ['class' => 'form-control input_number', 'placeholder' => __('sale.total_amount'), 'required', 'id' => 'final_total']); !!}
          </div>
        </div>
        <div class="col-sm-4">
          <div class="form-group">
            {!! Form::label('receipt_for', __('receipt::lang.receipt_for').':') !!}
            {!! Form::select('receipt_for', $users, $receipt->receipt_for, ['class' => 'form-control select2', 'placeholder' => __('messages.please_select')]); !!}
          </div>
        </div>
        <div class="col-sm-4">
          <div class="form-group">
            {!! Form::label('receipt_from', __('receipt::lang.receipt_from').':') !!}
            {!! Form::text('receipt_from', $receipt->receipt_from, ['class' => 'form-control', 'placeholder' => __('receipt::lang.receipt_from')]); !!}
          </div>
        </div>
        <div class="col-sm-4">
          <div class="form-group">
            {!! Form::label('contact_id', __('contact.contact').':') !!} 
            {!! Form::select('contact_id', $contacts, $receipt->contact_id, ['class' => 'form-control select2', 'placeholder' => __('messages.please_select')]); !!}
          </div>
        </div>
        <div class="clearfix"></div>
        <div class="col-sm-4">
            <div class="form-group">
                {!! Form::label('document', __('purchase.attach_document') . ':') !!}
                {!! Form::file('document', ['id' => 'upload_document', 'accept' => implode(',', array_keys(config('constants.document_upload_mimes_types')))]); !!}
                <small><p class="help-block">@lang('purchase.max_file_size', ['size' => (config('constants.document_size_limit') / 1000000)])
                @includeIf('components.document_help_text')</p></small>
                @if(!empty($receipt->document))
                    <p><strong>{{__('lang_v1.previous_file')}}:</strong> 
                        <a href="{{asset('uploads/documents/' . $receipt->document)}}" target="_blank">
                            <i class="fa fa-external-link"></i> {{$receipt->document}}
                        </a>
                    </p>
                @endif
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group">
                {!! Form::label('tax_id', __('product.applicable_tax') . ':' ) !!}
                <div class="input-group">
                    <span class="input-group-addon">
                        <i class="fa fa-info"></i>
                    </span>
                    {!! Form::select('tax_id', $taxes['tax_rates'], $receipt->tax_id, ['class' => 'form-control'], $taxes['attributes']); !!}
                </div>

            <input type="hidden" name="tax_calculation_amount" id="tax_calculation_amount" 
            value="0">
                </div>
            </div>
        </div>
        <div class="col-sm-4">
          <div class="form-group">
            {!! Form::label('additional_notes', __('receipt::lang.receipt_notes') . ':') !!}
                {!! Form::textarea('additional_notes', $receipt->additional_notes, ['class' => 'form-control', 'rows' => 3]); !!}
          </div>
        </div>
      </div>
    </div>
  </div> <!--box end-->
  <div class="col-sm-12 text-center">
    <button type="submit" class="tw-dw-btn tw-dw-btn-primary tw-text-white tw-dw-btn-lg">@lang('messages.update')</button>
  </div>

{!! Form::close() !!}
</section>
@stop
@section('javascript')
<script type="text/javascript">
  $(document).ready( function(){
    $('#receipt_transaction_date').datetimepicker({
        format: moment_date_format + ' ' + moment_time_format,
        ignoreReadonly: true,
    });
  });

  __page_leave_confirmation('#edit_receipt_form');

  $(document).on('change', '#tax_id', function(){
    var tax_percent = $(this).find(':selected').data('rate');
    var final_total = __read_number($('#final_total'));
    
    if(tax_percent){
      var tax_amount = __calculate_amount('percentage', tax_percent, final_total);
      $('#tax_calculation_amount').val(tax_amount);
    } else {
      $('#tax_calculation_amount').val(0);
    }
  });
</script>
@endsection
