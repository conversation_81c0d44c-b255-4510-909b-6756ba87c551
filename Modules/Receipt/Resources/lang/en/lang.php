<?php

return [
    'receipts' => 'Receipts',
    'receipt' => 'Receipt',
    'all_receipts' => 'All Receipts',
    'add_receipt' => 'Add Receipt',
    'edit_receipt' => 'Edit Receipt',
    'view_receipt' => 'View Receipt',
    'delete_receipt' => 'Delete Receipt',
    'receipt_details' => 'Receipt Details',
    'receipt_categories' => 'Receipt Categories',
    'manage_receipt_categories' => 'Manage Receipt Categories',
    'category_code' => 'Category Code',
    'category_code_help' => 'Receipt category code will be used to generate receipt reference number',
    'receipt_for' => 'Receipt For',
    'receipt_from' => 'Receipt From',
    'receipt_date' => 'Receipt Date',
    'receipt_amount' => 'Receipt Amount',
    'receipt_category' => 'Receipt Category',
    'receipt_reference_no' => 'Receipt Reference No.',
    'receipt_added_success' => 'Receipt added successfully',
    'receipt_updated_success' => 'Receipt updated successfully',
    'receipt_deleted_success' => 'Receipt deleted successfully',
    'receipt_created_notification' => 'New receipt has been created',
    'no_receipts_found' => 'No receipts found',
    'receipt_list' => 'Receipt List',
    'total_receipt' => 'Total Receipt',
    'receipt_report' => 'Receipt Report',
    'receipt_summary' => 'Receipt Summary',
    'receipt_by_category' => 'Receipt by Category',
    'receipt_by_location' => 'Receipt by Location',
    'receipt_by_user' => 'Receipt by User',
    'receipt_payment_status' => 'Receipt Payment Status',
    'paid_receipts' => 'Paid Receipts',
    'unpaid_receipts' => 'Unpaid Receipts',
    'partially_paid_receipts' => 'Partially Paid Receipts',
    'receipt_document' => 'Receipt Document',
    'upload_receipt_document' => 'Upload Receipt Document',
    'receipt_notes' => 'Receipt Notes',
    'additional_notes' => 'Additional Notes',
    'receipt_tax' => 'Receipt Tax',
    'receipt_total' => 'Receipt Total',
    'receipt_contact' => 'Receipt Contact',
    'receipt_location' => 'Receipt Location',
    'receipt_created_by' => 'Receipt Created By',
    'receipt_payments' => 'Receipt Payments',
    'add_receipt_payment' => 'Add Receipt Payment',
    'receipt_payment_added' => 'Receipt payment added successfully',
    'receipt_payment_updated' => 'Receipt payment updated successfully',
    'receipt_payment_deleted' => 'Receipt payment deleted successfully',
    'print_receipt' => 'Print Receipt',
    'receipt_print_format' => 'Receipt Print Format',
    'receipt_template' => 'Receipt Template',
    'receipt_settings' => 'Receipt Settings',
    'enable_receipt_module' => 'Enable Receipt Module',
    'receipt_prefix' => 'Receipt Prefix',
    'receipt_number_format' => 'Receipt Number Format',
    'receipt_auto_generate' => 'Auto Generate Receipt Number',
    'receipt_recurring' => 'Recurring Receipt',
    'recurring_receipt' => 'Recurring Receipt',
    'receipt_recur_interval' => 'Receipt Recur Interval',
    'receipt_recur_repetitions' => 'Receipt Recur Repetitions',
    'receipt_recur_stopped_on' => 'Receipt Recur Stopped On',
    'receipt_parent' => 'Receipt Parent',
    'receipt_child' => 'Receipt Child',
    'receipt_recurring_settings' => 'Receipt Recurring Settings',
    'receipt_recurring_enabled' => 'Receipt Recurring Enabled',
    'receipt_recurring_disabled' => 'Receipt Recurring Disabled',
];
