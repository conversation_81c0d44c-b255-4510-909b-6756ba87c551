<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware(['web', 'auth', 'SetSessionData', 'language', 'timezone', 'AdminSidebarMenu'])->prefix('receipt')->group(function() {
    Route::get('/', 'ReceiptController@index')->name('receipt.index');
    Route::get('/create', 'ReceiptController@create')->name('receipt.create');
    Route::post('/', 'ReceiptController@store')->name('receipt.store');
    Route::get('/{id}', 'ReceiptController@show')->name('receipt.show');
    Route::get('/{id}/edit', 'ReceiptController@edit')->name('receipt.edit');
    Route::put('/{id}', 'ReceiptController@update')->name('receipt.update');
    Route::delete('/{id}', 'ReceiptController@destroy')->name('receipt.destroy');
});
