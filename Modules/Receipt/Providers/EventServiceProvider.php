<?php

namespace Modules\Receipt\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Receipt\Events\ReceiptCreatedOrModified;
use Modules\Receipt\Listeners\ReceiptTransactionListener;
use App\Events\TransactionPaymentAdded;
use App\Events\TransactionPaymentDeleted;
use App\Events\TransactionPaymentUpdated;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        ReceiptCreatedOrModified::class => [
            ReceiptTransactionListener::class,
        ],
        TransactionPaymentAdded::class => [
            ReceiptTransactionListener::class . '@handlePaymentAdded',
        ],
        TransactionPaymentUpdated::class => [
            ReceiptTransactionListener::class . '@handlePaymentUpdated',
        ],
        TransactionPaymentDeleted::class => [
            ReceiptTransactionListener::class . '@handlePaymentDeleted',
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
