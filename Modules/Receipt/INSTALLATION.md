# Hướng dẫn Cài đặt Module Receipt

## Bước 1: <PERSON><PERSON><PERSON> tra yêu cầu hệ thống

Đảm bảo hệ thống của bạn đáp ứng các yêu cầu sau:
- UltimatePOS đã được cài đặt và hoạt động
- PHP >= 7.4
- <PERSON><PERSON> >= 8.0
- MySQL/MariaDB
- Quyề<PERSON> write vào thư mục `Modules`

## Bước 2: Cài đặt Module

### 2.1. Copy Module
```bash
# Copy thư mục Receipt vào thư mục Modules của UltimatePOS
cp -r Receipt /path/to/ultimatepos/Modules/
```

### 2.2. Ch<PERSON>y Migration
```bash
cd /path/to/ultimatepos
php artisan module:migrate Receipt
```

### 2.3. Clear Cache
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
```

## Bước 3: <PERSON><PERSON><PERSON> hình Permissions

### 3.1. <PERSON><PERSON><PERSON> cập Admin Panel
1. Đ<PERSON>ng nhập vào UltimatePOS với tài khoản admin
2. Vào **Settings** > **Roles & Permissions**

### 3.2. Cấp quyền cho Role
Chọn role cần cấp quyền và tick vào các permissions sau:
- ☑️ **View Receipt** (`receipt.view`)
- ☑️ **Add Receipt** (`receipt.create`) 
- ☑️ **Edit Receipt** (`receipt.update`)
- ☑️ **Delete Receipt** (`receipt.delete`)

### 3.3. Lưu cài đặt
Click **Save** để lưu cài đặt permissions.

## Bước 4: Kiểm tra cài đặt

### 4.1. Kiểm tra Menu
Sau khi cài đặt thành công, bạn sẽ thấy menu **"Receipts"** xuất hiện trong sidebar với các submenu:
- All Receipts
- Add Receipt  
- Receipt Categories

### 4.2. Test chức năng cơ bản
1. Vào **Receipts** > **Add Receipt**
2. Điền thông tin cơ bản:
   - Business Location: Chọn location
   - Date: Chọn ngày
   - Total Amount: Nhập số tiền
3. Click **Save**
4. Kiểm tra phiếu thu đã được tạo trong **All Receipts**

## Bước 5: Tạo dữ liệu mẫu (Tùy chọn)

Để tạo dữ liệu mẫu cho việc test:
```bash
php artisan module:seed Receipt
```

## Bước 6: Cấu hình bổ sung

### 6.1. Cấu hình Receipt Categories
1. Vào **Settings** > **Taxonomies**
2. Chọn type **"Receipt Category"**
3. Thêm các danh mục thu nhập như:
   - Sales Revenue
   - Service Income
   - Commission
   - Interest Income
   - Other Income

### 6.2. Cấu hình Payment Methods
Đảm bảo các phương thức thanh toán đã được cấu hình trong:
**Settings** > **Business Settings** > **Payment Methods**

## Troubleshooting

### Lỗi thường gặp và cách khắc phục:

#### 1. Menu không xuất hiện
**Nguyên nhân**: Chưa có permissions hoặc cache chưa được clear
**Giải pháp**:
```bash
php artisan cache:clear
php artisan config:clear
```
Kiểm tra lại permissions cho user/role.

#### 2. Lỗi Migration
**Nguyên nhân**: Database connection hoặc permissions
**Giải pháp**:
```bash
# Kiểm tra database connection
php artisan migrate:status

# Chạy lại migration
php artisan module:migrate-refresh Receipt
```

#### 3. Lỗi 500 khi truy cập
**Nguyên nhân**: Lỗi code hoặc missing dependencies
**Giải pháp**:
- Kiểm tra log: `storage/logs/laravel.log`
- Kiểm tra PHP error log
- Đảm bảo tất cả files đã được copy đúng

#### 4. Không thể tạo Receipt
**Nguyên nhân**: Thiếu Business Location hoặc User
**Giải pháp**:
- Đảm bảo đã tạo ít nhất 1 Business Location
- Đảm bảo user có quyền `receipt.create`

## Gỡ cài đặt (Nếu cần)

Để gỡ bỏ module:
```bash
# Rollback migrations
php artisan module:migrate-rollback Receipt

# Xóa thư mục module
rm -rf Modules/Receipt

# Clear cache
php artisan cache:clear
php artisan config:clear
```

## Hỗ trợ

Nếu gặp vấn đề trong quá trình cài đặt:
1. Kiểm tra log files trong `storage/logs/`
2. Đảm bảo tất cả requirements đã được đáp ứng
3. Kiểm tra permissions của files và thư mục
4. Liên hệ support team nếu cần thiết

## Cập nhật Module

Khi có phiên bản mới:
1. Backup database và files
2. Copy files mới
3. Chạy migration: `php artisan module:migrate Receipt`
4. Clear cache: `php artisan cache:clear`

---

**Chúc bạn sử dụng module thành công!** 🎉
