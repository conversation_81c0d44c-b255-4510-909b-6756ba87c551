<?php

namespace Modules\Receipt\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Eloquent\Model;
use App\Transaction;
use App\BusinessLocation;
use App\Contact;
use App\User;
use Carbon\Carbon;

class ReceiptSampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Model::unguard();

        // Get first business from database
        $business_location = BusinessLocation::first();
        if (!$business_location) {
            $this->command->error('No business location found. Please create a business first.');
            return;
        }

        $business_id = $business_location->business_id;
        
        // Get first user
        $user = User::where('business_id', $business_id)->first();
        if (!$user) {
            $this->command->error('No user found for this business.');
            return;
        }

        // Get first contact (customer)
        $contact = Contact::where('business_id', $business_id)
            ->where('type', 'customer')
            ->first();

        $this->command->info('Creating sample receipt data...');

        // Sample receipts data
        $sample_receipts = [
            [
                'ref_no' => 'REC-001',
                'receipt_for' => $user->id,
                'receipt_from' => 'Customer Payment',
                'final_total' => 1500000,
                'additional_notes' => 'Payment for invoice #INV-001',
                'transaction_date' => Carbon::now()->subDays(5),
            ],
            [
                'ref_no' => 'REC-002',
                'receipt_for' => $user->id,
                'receipt_from' => 'Service Revenue',
                'final_total' => 2500000,
                'additional_notes' => 'Service consultation fee',
                'transaction_date' => Carbon::now()->subDays(3),
            ],
            [
                'ref_no' => 'REC-003',
                'receipt_for' => $user->id,
                'receipt_from' => 'Product Sales',
                'final_total' => 3200000,
                'additional_notes' => 'Product sales revenue',
                'transaction_date' => Carbon::now()->subDays(1),
            ],
            [
                'ref_no' => 'REC-004',
                'receipt_for' => $user->id,
                'receipt_from' => 'Commission Income',
                'final_total' => 800000,
                'additional_notes' => 'Commission from partner sales',
                'transaction_date' => Carbon::now(),
            ],
            [
                'ref_no' => 'REC-005',
                'receipt_for' => $user->id,
                'receipt_from' => 'Interest Income',
                'final_total' => 450000,
                'additional_notes' => 'Bank interest income',
                'transaction_date' => Carbon::now()->subDays(7),
            ],
        ];

        foreach ($sample_receipts as $receipt_data) {
            $transaction_data = [
                'business_id' => $business_id,
                'location_id' => $business_location->id,
                'type' => 'receipt',
                'status' => 'final',
                'payment_status' => 'paid',
                'contact_id' => $contact ? $contact->id : null,
                'ref_no' => $receipt_data['ref_no'],
                'transaction_date' => $receipt_data['transaction_date'],
                'total_before_tax' => $receipt_data['final_total'],
                'tax_amount' => 0,
                'final_total' => $receipt_data['final_total'],
                'receipt_for' => $receipt_data['receipt_for'],
                'receipt_from' => $receipt_data['receipt_from'],
                'additional_notes' => $receipt_data['additional_notes'],
                'created_by' => $user->id,
                'created_at' => $receipt_data['transaction_date'],
                'updated_at' => $receipt_data['transaction_date'],
            ];

            $receipt = Transaction::create($transaction_data);

            $this->command->info("Created receipt: {$receipt->ref_no} - Amount: " . number_format($receipt->final_total));
        }

        $this->command->info('Sample receipt data created successfully!');
        $this->command->info('You can now view the receipts in the admin panel under Receipts menu.');
    }
}
