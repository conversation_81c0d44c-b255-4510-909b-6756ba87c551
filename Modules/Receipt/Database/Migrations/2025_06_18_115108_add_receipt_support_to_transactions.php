<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Modify transactions table to support 'receipt' type
        DB::statement("ALTER TABLE transactions MODIFY COLUMN type ENUM('purchase','sell', 'expense', 'receipt')");

        // Add receipt-specific columns to transactions table
        Schema::table('transactions', function (Blueprint $table) {
            $table->integer('receipt_category_id')->nullable()->after('expense_category_id');
            $table->string('receipt_for')->nullable()->after('expense_for');
            $table->string('receipt_from')->nullable()->after('receipt_for');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove receipt-specific columns
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn(['receipt_category_id', 'receipt_for', 'receipt_from']);
        });

        // Revert transactions table type enum
        DB::statement("ALTER TABLE transactions MODIFY COLUMN type ENUM('purchase','sell', 'expense')");
    }
};
