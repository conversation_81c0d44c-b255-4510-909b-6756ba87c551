<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Insert receipt permissions
        $permissions = [
            ['name' => 'receipt.view', 'guard_name' => 'web'],
            ['name' => 'receipt.create', 'guard_name' => 'web'],
            ['name' => 'receipt.update', 'guard_name' => 'web'],
            ['name' => 'receipt.delete', 'guard_name' => 'web'],
        ];

        foreach ($permissions as $permission) {
            \Spatie\Permission\Models\Permission::create($permission);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove receipt permissions
        $permissions = ['receipt.view', 'receipt.create', 'receipt.update', 'receipt.delete'];

        foreach ($permissions as $permission) {
            \Spatie\Permission\Models\Permission::where('name', $permission)->delete();
        }
    }
};
