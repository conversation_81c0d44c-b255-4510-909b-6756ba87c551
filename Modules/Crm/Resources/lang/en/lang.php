<?php

return [
    'crm' => 'CRM',
    'add_login' => 'Add Login',
    'edit_login' => 'Edit Login',
    'schedules' => 'Schedules',
    'schedule' => 'Follow Up',
    'title' => 'Title',
    'start_datetime' => 'Start Datetime',
    'end_datetime' => 'End Datetime',
    'schedule_type' => 'Follow Up Type',
    'send_notification' => 'Send Notification',
    'notify_via' => 'Notify Via',
    'notify_before' => 'Notify Before',
    'assgined' => 'Assgined to',
    'add_schedule' => 'Add Follow Up',
    'sms' => 'Sms',
    'meeting' => 'Meeting',
    'call' => 'Call',
    'minute' => 'Minute',
    'description' => 'Description',
    'hour' => 'Hour',
    'all_schedules' => 'All Follow ups',
    'schedule_info' => 'Follow Up Info',
    'schedule_log' => 'Follow Up Log',
    'email' => 'Email',
    'edit_schedule' => 'Edit Follow Up',
    'subject' => 'Subject',
    'log_type' => 'Log Type',
    'add_schedule_log' => 'Add Follow Up Log',
    'edit_schedule_log' => 'Edit Follow Up Log',
    'schedule_notification' => ':created_by invited you for the follow up :title starts at :startdatetime',
    'no_log_found' => 'No Log Found!',
    'crm_module' => 'Crm Module',
    'todays_schedule' => "Today's Follow Ups",
    'no_schedule_for_today' => 'No follow up found for today',
    'sources' => 'Sources',
    'life_stage' => 'Life Stage',
    'source' => 'Source',
    'manage_source' => 'Manage Source',
    'manage_life_stage' => 'Manage Life Stage',
    'lead' => 'Lead',
    'leads' => 'Leads',
    'all_leads' => 'All Leads',
    'convert_to_customer' => 'Convert to customer',
    'view_lead' => 'View Lead',
    'lead_info' => 'Lead info',
    'open' => 'Open',
    'completed' => 'Completed',
    'canceled' => 'Cancelled',
    'scheduled' => 'Scheduled',
    'double_click_on_any_day_to_add_new_schedule' => 'Double click on any day to add new follow up.',
    'documents_and_notes' => 'Documents & Note',
    'schedule_status' => 'Follow Up Status',
    'send_schedule_notificatoion' => 'Auto Notification will be sent at specified time before follow up starts.',
    'campaigns' => 'Campaigns',
    'campaign_type' => 'Campaign Type',
    'all_campaigns' => 'All Campaigns',
    'create_campaign' => 'Create Campaign',
    'campaign_name' => 'Campaign Name',
    'edit_campaign' => 'Edit Campaign',
    'sms_body' => 'Sms Body',
    'email_body' => 'Email Body',
    'source_are_used_when_lead_is_added' => 'Sources are used when adding leads',
    'lifestage_of_leads' => 'Life stage of leads',
    'sent' => 'Sent',
    'sent_on' => 'Sent On',
    'created_this_campaign_on' => ':name created this campaign on ',
    'leads_and_customers' => 'Leads and Customers',
    'contacts_login' => 'Contacts Login',
    'all_contacts_login' => 'All Contacts Login',
    'life_stages' => 'Life Stages',
    'birthdays' => 'Birthdays',
    'upcoming' => 'Upcoming',
    'birthday_on' => 'Birthday on',
    'send_wishes' => 'Send wishes',
    'plz_select_user' => 'Please select the users to send wishes.',
    'access_all_schedule' => 'Access all follow up',
    'access_own_schedule' => 'Access own follow up',
    'access_all_leads' => 'Access all leads',
    'access_own_leads' => 'Access own leads',
    'access_all_campaigns' => 'Access all campaigns',
    'access_own_campaigns' => 'Access own campaigns',
    'access_contact_login' => 'Access contact login',
    'access_sources' => 'Access sources',
    'access_life_stage' => 'Access life stage',
    'conversion' => 'Conversion',
    'follow_ups' => 'Follow ups',
    'list_view' => 'List View',
    'kanban_board' => 'Kanban Board',
    'last_followed_up_on' => 'Last followed up on',
    'view_follow_up' => 'View follow up',
    'last_follow_up' => 'Last follow up',
    'upcoming_follow_up' => 'Upcoming follow up',
    'additional_info' => 'Additional info',
    'converted_from_leads' => 'Converted to customer from leads',
    'add_contact_persons' => 'Add Contact Persons',
    'add_contact_person' => 'Add contact person :number',
    'contact_persons' => 'Contact Persons',
    'follow_ups_by_user' => 'Follow ups by user',
    'total_follow_ups' => 'Total follow ups',
    'follow_ups_by_contacts' => 'Follow ups by contacts',
    'to' => 'To',
    'transaction_activity' => 'Transaction activity',
    'has_transactions' => 'Has transactions',
    'has_no_transactions' => 'Has no transactions',
    'in_days' => 'In days',
    'in' => 'In',
    'lead_to_customer_conversion' => 'Leads to customer conversion',
    'converted_by' => 'Converted By',
    'converted_on' => 'Converted On',
    'call_log' => 'Call Log',
    'call_type' => 'Call Type',
    'call_duration' => 'Call Duration',
    'view_own_call_log' => 'View own call log',
    'view_all_call_log' => 'View all call log',
    'call_log_created_by' => 'Call log created by',
    'cancelled' => 'Cancelled',
    'add_advance_follow_up' => 'Add advance follow up',
    'follow_up_by' => 'Follow up by',
    'no_transactions_since' => 'No transactions since (In months)',
    'select_invoices' => 'Select invoices',
    'enter_days' => 'Enter days',
    'there_is_no_customer_to_add_follow_up' => 'There is no customer to add follow up',
    'multiple_followups_will_be_created' => 'Multiple Follow ups will be created',
    'proposal_template' => 'Proposal template',
    'proposals' => 'Proposals',
    'proposal' => 'Proposal',
    'attachments' => 'Attachments',
    'template_is_already_created' => 'Template is already created',
    'send' => 'Send',
    'access_proposal' => 'Access proposal',
    'please_add_template' => 'Please add template',
    'document' => 'Document',
    'no_template_found' => 'No proposal template found!',
    'send_to' => 'Send to',
    'sent_by' => 'Sent by',
    'proposal_sent_to' => 'Proposal sent to <b> :name</b>',
    'add_onetime_follow_up' => 'Add one time follow up',
    'add_recursive_follow_up' => 'Add recurring follow up',
    'generate_after' => 'Generate after',
    'recur_follow_ups' => 'Recurring Follow up',
    'follow_up_help' => '<b>Payment status:</b> Automatic followup will be added if 
                    payment status due/partial/overdue for specified days <br><br>
                    <b>Orders:</b> Automatic followup will be added if no order in specified days',
    'order_request' => 'Order Request',
    'add_order_request' => 'Add Order Request',
    'enable_order_request' => 'Enable Order Request',
    'enable_order_request_help' => 'If enabled contact user will be able to place an order request',
    'order_request_prefix' => 'Order Request Prefix',
    'my_followups' => 'My Follow ups',
    'my_leads' => 'My Leads',
    'my_leads_to_customer_conversion' => 'My leads to customer conversion',
    'todays_followups' => "Today's Follow ups",
    'my_call_logs' => 'My call logs',
    'calls_today' => 'Calls Today',
    'calls_yesterday' => 'Calls Yesterday',
    'calls_this_month' => 'Calls this month',
    'all_users_call_log' => 'All users call log',
    'b2b_marketplace' => 'B2B Marketplace',
    'key' => 'Key',
    'import_leads' => 'Import Leads',
    'followup_assigned_to' => 'Assigned to',
    'access_b2b_marketplace' => 'Access B2B Marketplace',
    'view' => 'View',
    'none' => 'None',
    'followup_category' => 'Followup Category',
    'manage_followup_category' => 'Manage Followup Category',
    'commissions' => 'Commissions',
    'contact_person' => 'Contact person',
    'total_commission' => 'Total commission',
    'comma_separated_email' => 'Comma separated values of emails',
];
