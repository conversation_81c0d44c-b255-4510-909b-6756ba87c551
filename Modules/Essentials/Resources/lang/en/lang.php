<?php

return [
    'essentials' => 'Essentials',
    'document' => 'Document',
    'description' => 'Description',
    'submit' => 'Submit',
    'delete' => 'Delete',
    'download' => 'Download',
    'name' => 'Name',
    'created_at' => 'Created Date',
    'action' => 'Action',
    'all_documents' => 'All documents',
    'manage_document' => 'Manage all your documents',
    'share' => 'Share',
    'share_document' => 'Share documents',
    'user' => 'User',
    'role' => 'Role',
    'shared_by' => 'Shared By :',
    'todo' => 'To Do',
    'todo_list' => 'To Do List',
    'task' => 'Task',
    'date' => 'Date',
    'no_task' => "You don't have any task!",
    'cancel' => 'Cancel',
    'memos' => 'Memos',
    'manage_memos' => 'Manage all your memos',
    'all_memos' => 'All memos',
    'share_memos' => 'Share memos',
    'view' => 'View',
    'heading' => 'Heading',
    'reminders' => 'Reminders',
    'event_name' => 'Event Name',
    'time' => 'Time',
    'repeat' => 'Repeat',
    'one_time' => 'One time',
    'every_day' => 'Every day',
    'every_week' => 'Every week',
    'every_month' => 'Every month',
    'choose_anyone' => 'Please choose anyone',
    'add_reminder' => 'Add reminder',
    'reminder_details' => 'Reminder Details',
    'delete_reminder' => 'Delete reminder',
    'change_reminder_repeat' => 'Change reminder repeat',
    'uploaded_date' => 'Uploaded Date',
    'previous_date' => 'Previous Date',
    'next_date' => 'Next Date',
    'document_share_notification' => '<i>:shared_by</i> shared a document - <i>:document_name</i>',
    'memo' => 'Memo',
    'messages' => 'Messages',
    'type_message' => 'Type message...',
    'create_message' => 'Create Message',
    'view_message' => 'View Message',
    'essentials_module' => 'Essentials Module',
    'new_message_notification' => 'You have new messages from :sender',
    'hrm' => 'HRM',
    'leave_type' => 'Leave Type',
    'all_leave_types' => 'All leave types',
    'max_leave_count' => 'Max Leave Count',
    'add_leave_type' => 'Add Leave Type',
    'edit_leave_type' => 'Edit Leave Type',
    'leave' => 'Leave',
    'all_leaves' => 'All Leaves',
    'end_date' => 'End Date',
    'start_date' => 'Start Date',
    'reason' => 'Reason',
    'add_leave' => 'Add Leave',
    'approved' => 'Approved',
    'change_status' => 'Change Status',
    'cancelled' => 'Cancelled',
    'employee' => 'Employee',
    'upcoming' => 'Upcoming',
    'past' => 'Past',
    'now' => 'Now',
    'new_leave_notification' => ':employee applied for a leave. Reference No - <i>:ref_no</i>',
    'status_change_notification' => ':admin :status your leave. Reference No - <i>:ref_no</i>',
    'essentials_settings' => 'Essentials Settings',
    'leave_ref_no_prefix' => 'Leave Reference No. prefix',
    'leave_instructions' => 'Leave Instructions',
    'attendance' => 'Attendance',
    'all_attendance' => 'All Attendance',
    'add_attendance' => 'Add Attendance',
    'clock_in_time' => 'Clock in time',
    'clock_out_time' => 'Clock out time',
    'employees' => 'Employees',
    'ip_address' => 'IP Address',
    'clock_in_note' => 'Clock in note',
    'clock_out_note' => 'Clock out note',
    'clock_in' => 'Clock In',
    'clock_out' => 'Clock Out',
    'clock_in_success' => 'Clocked In successfully',
    'clock_out_success' => 'Clocked Out successfully',
    'not_allowed' => 'Not allowed',
    'edit_attendance' => 'Edit Attendance',
    'clocked_in_at' => 'Clocked In at',
    'clock_in_clock_out' => 'Clock In - Clock Out',
    'activity' => 'Activity',
    'status_changed_to' => 'Status changed to :status',
    'leaves_summary_for_user' => 'Leaves Summary for :user',
    'total_work_hours' => 'Total work hours',
    'leave_count_interval' => 'Leave count interval',
    'current_month' => 'Current month',
    'current_fy' => 'Current financial year',
    'remaining' => 'Remaining',
    'max_allowed_leaves' => 'Maximum allowed leaves',
    'within_current_month' => 'Within current month',
    'within_current_fy' => 'Within current financial year',
    'payroll' => 'Payroll',
    'all_payrolls' => 'All Payrolls',
    'proceed' => 'Proceed',
    'month_year' => 'Month/Year',
    'add_payroll' => 'Add Payroll',
    'edit_payroll' => 'Edit Payroll',
    'payroll_of_employee' => 'Payroll of <strong>:employee</strong> for <strong>:date</strong>',
    'total_work_duration' => 'Total work duration',
    'amount_per_unit_duartion' => 'Amount per unit duration',
    'allowances' => 'Earnings',
    'allowance' => 'Earning',
    'deductions' => 'Deductions',
    'deduction' => 'Deduction',
    'duration_unit' => 'Duration Unit',
    'gross_amount' => 'Gross Amount',
    'payroll_ref_no_prefix' => 'Payroll Reference No. prefix',
    'payroll_added_notification' => 'Payroll for :month_year added by :created_by. Reference No. :ref_no',
    'payroll_updated_notification' => 'Payroll for :month_year revised by :created_by. Reference No. :ref_no',
    'holiday' => 'Holiday',
    'all_holidays' => 'All Holidays',
    'add_holiday' => 'Add Holiday',
    'edit_holiday' => 'Edit Holiday',
    'payroll_for' => 'Payroll For',
    'total_payroll' => 'Total Payroll',
    'approve_leave' => 'Approve Leave',
    'amount_type' => 'Amount Type',
    'applicable_date' => 'Applicable Date',
    'applicable_date_help' => 'Based on selected date it will be shown to the payroll. If no date is selected then will be shown in all new payroll created. You can modify this value in payroll.',
    'assigned_to' => 'Assigned To',
    'estimated_hours' => 'Estimated Hours',
    'add_to_do' => 'Add To Do',
    'edit_to_do' => 'Edit To Do',
    'assign_todos' => "Assign To Do's to others",
    'assigned_by' => 'Assigned By',
    'comments' => 'Comments',
    'add_comment' => 'Add Comment',
    'upload' => 'Upload',
    'assigned_to' => 'Assigned To',
    'mark_as_complted' => 'Mark as completed',
    'new' => 'New',
    'in_progress' => 'In-Progress',
    'on_hold' => 'On Hold',
    'low' => 'Low',
    'medium' => 'Medium',
    'high' => 'High',
    'urgent' => 'Urgent',
    'priority' => 'Priority',
    'change_status' => 'Change Status',
    'task_id' => 'Task Id',
    'essentials_n_hrm_settings' => 'Essentials and HRM Settings',
    'essentials_todos_prefix' => 'Todos ID Prefix',
    'new_task_notification' => ':assigned_by added you to a new task <br> Task ID - :task_id',
    'new_task_comment_notification' => ':added_by commented on the task - :task_id assigned to you',
    'new_task_document_notification' => ':uploaded_by uploaded a new document to the task - :task_id assigned to you',
    'clock_in_clock_out_validation_msg' => 'Clock in and Clock out time cannot be overlapped with exsting attendance',
    'departments' => 'Departments',
    'manage_departments' => 'Manage Departments',
    'department_id' => 'Department ID',
    'department_code_help' => 'Enter unique department ID',
    'designations' => 'Designations',
    'manage_designations' => 'Manage designations',
    'designation_code_help' => 'Enter unique designation ID',
    'department' => 'Department',
    'designation' => 'Designation',
    'hrm_details' => 'HRM Details',
    'document_shared' => 'Document Shared',
    'leave_status_changed' => 'Leave status changed',
    'leave_added_successfully' => 'Leave added successfully',
    'new_message' => 'New Messsage',
    'new_comment' => 'New comment',
    'new_document' => 'New document',
    'new_task_added' => 'New task added',
    'payroll_added' => 'Payroll added',
    'payroll_updated' => 'Payroll updated',
    'shifts' => 'Shifts',
    'add_shift' => 'Add Shift',
    'assign_users' => 'Assign Users',
    'grace_before_checkin' => 'Grace before checkin',
    'grace_before_checkin_help' => '(in minute) this time will not counted as overtime',
    'grace_after_checkin' => 'Grace after checkin',
    'grace_after_checkin_help' => '(in minute) this time will not counted as late',
    'grace_before_checkout' => 'Grace before checkout',
    'grace_before_checkout_help' => '(in minute) this time will not counted as early left',
    'grace_after_checkout' => 'Grace after checkout',
    'grace_time' => 'Grace Time',
    'shift_not_allocated' => 'No active shift for this time',
    'shift_not_over' => 'Shift not over',
    'attendance_by_shift' => 'Attendance by shift',
    'attendance_by_date' => 'Attendance by date',
    'present' => 'Present',
    'absent' => 'Absent',
    'shift' => 'Shift',
    'no_data_found' => 'No data found',
    'import_attendance' => 'Import Attendance',
    'email_ins' => 'Email id of the user',
    'clock_in_time_ins' => 'Clock in time in "Y-m-d H:i:s" format',
    'clock_out_time_ins' => 'Clock out time in "Y-m-d H:i:s" format',
    'add_latest_attendance' => 'Add latest attendance',
    'select_employee' => 'Select employee',
    'shift_type' => 'Shift Type',
    'fixed_shift' => 'Fixed shift',
    'flexible_shift' => 'Flexible shift',
    'work_duration' => 'Work Duration',
    'shift_type_tooltip' => '<b>Fixed Shift:</b> Fixed Clock in & Clock out time with grace time. <br>
        <b>Flexible Shift:</b> No Clock in & Clock out time.',
    'shift_datatable_tooltip' => 'Manage shifts and assign shift to users',
    'clocked_in' => 'Clocked in',
    'upcoming_holidays' => 'Upcoming holidays',
    'leaves' => 'Leaves',
    'my_leaves' => 'My leaves',
    'holidays' => 'Holidays',
    'birthdays' => 'Birthdays',
    'todays_attendance' => "Today's Attendance",
    'already_clocked_in' => 'Already clocked in',
    'not_clocked_in' => 'Not clocked in',
    'crud_leave_type' => 'Add/Edit/View/Delete leave type',
    'crud_all_leave' => 'Add/Edit/View/Delete all leave',
    'crud_own_leave' => 'Add/View own leave',
    'crud_all_attendance' => 'Add/Edit/View/Delete all attendance',
    'view_own_attendance' => 'View own attendance',
    'crud_department' => 'Add/Edit/View/Delete department',
    'crud_designation' => 'Add/Edit/View/Delete designation',
    'knowledge_base' => 'Knowledge Base',
    'content' => 'Content',
    'title' => 'Title',
    'add_knowledge_base' => 'Add knowledge base',
    'add_section' => 'Add Section',
    'add_article' => 'Add Article',
    'private' => 'Private',
    'public' => 'Public',
    'share_with' => 'Share with',
    'share_only_with' => 'Share only with',
    'only_with' => 'Only with',
    'edit_knowledge_base' => 'Edit knowledge base',
    'edit_section' => 'Edit section',
    'edit_article' => 'Edit article',
    'docs' => 'Docs',
    'view_shared_docs' => 'View shared documents',
    'spreadsheets' => 'Spreadsheets',
    'no_docs_found' => 'No documents found!',
    'timing' => 'Timing',
    'your_shifts' => 'Your shifts',
    'clock_in_location' => 'Clock in location',
    'clock_out_location' => 'Clock out location',
    'is_location_required' => 'Is location required?',
    'you_must_enable_location' => 'Location is required, you must enable location',
    'allow_location' => 'Allow location',
    'salary' => 'Basic salary',
    'pay_cycle' => 'Pay Cycle',
    'week' => 'Week',
    'per' => 'Per',
    'pay_components' => 'Pay Components',
    'add_pay_component' => 'Add Pay Component',
    'edit_pay_component' => 'Edit Pay Componentt',
    'view_pay_component' => 'View Pay Component',
    'rate' => 'Rate',
    'total_earnings' => 'Total earnings',
    'total_deductions' => 'Total deductions',
    'net_pay' => 'Net pay',
    'payslip_for_the_month' => 'Payslip for the month of :month :year',
    'in_words' => 'In words',
    'days_present' => 'Days present',
    'days_absent' => 'Days absent',
    'sale_commission' => 'Sale Commission',
    'payroll_already_added_for_given_user' => 'Payroll has been added already, for given users',
    'notify_employee' => 'Send Notification',
    'payroll_for_month' => 'Payroll for <strong>:date</strong>',
    'payroll_group_name' => 'Payroll group name',
    'all_payroll_groups' => 'All payroll groups',
    'view_payroll_group' => 'View payroll group',
    'payroll_group' => 'Payroll group',
    'add_payment_for_payroll_group' => 'Add payment for payroll group',
    'group_status_tooltip' => 'If status is <code>final</code> then <code>payment can be added</code> otherwise not',
    'total_gross_amount' => 'Total gross amount',
    'allow_users_for_attendance_moved_to_role' => '"Allow users to enter their own attendance" setting has been moved to role.',
    'allow_users_for_attendance_from_web' => 'Allow users to enter their own attendance from web',
    'allow_users_for_attendance_from_api' => 'Allow users to enter their own attendance from api',
    'allow_auto_clockout' => 'Do auto clock out',
    'allow_auto_clockout_tooltip' => 'User will be auto clocked out after given time',
    'auto_clockout_time' => 'Auto clock out time',
    'work_duration_hour' => ':duration hour',
    'total_leaves_days' => ':total_leaves days',
    'my_payrolls' => 'My Payrolls',
    'view_all_payroll' => 'View all Payroll',
    'delete_payroll' => 'Delete Payroll',
    'access_sales_target' => 'Access Sales Targets',
    'sales_target' => 'Sales Targets',
    'set_sales_target' => 'Set Sales Target',
    'set_sales_target_for' => 'Set Sales Target for :user',
    'total_sales_amount_from' => 'Total sales amount from',
    'total_sales_amount_to' => 'Total sale amount to',
    'commission_percent' => 'Commission Percent',
    'sales_target_commission' => 'Sales Target Commission',
    'calculate_sales_target_commission_without_tax' => 'Calculate Sales Target Commission without Tax',
    'calculate_sales_target_commission_without_tax_help' => 'If checked sales target commission will be calculated on total sales by the employee without including taxes',
    'targets' => 'Targets',
    'target_achieved_this_month' => 'Target achieved this month',
    'target_achieved_last_month' => 'Target achieved last month',
    'my_sales_targets' => 'My sales targets',
    'sales_targets' => 'Sales targets',
    'get_current_location' => 'Get current location',
    'payroll_cant_be_deleted_if_final' => 'Payroll can not be deleted if status is final',
    'add_todos' => "Add To Do's",
    'edit_todos' => "Edit To Do's",
    'delete_todos' => "Delete To Do's",
    'attendance_report' => 'Attendance report',
    'is_present' => 'Is Present',
    'days_present' => 'Days Present',
    'days_absent' => 'Days Absent',
    'attendace_report_for' => 'Attendance Report for',
];
