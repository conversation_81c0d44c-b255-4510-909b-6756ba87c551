<?php

return [
    'essentials' => 'Essentials',
    'document' => 'Belge',
    'description' => 'Açıklama',
    'submit' => 'Sunmak',
    'delete' => 'Sil',
    'download' => 'İndir',
    'name' => 'Ad',
    'created_at' => 'Oluşturma Tarihi',
    'action' => 'Aksiyon',
    'all_documents' => 'Bütün belgeler',
    'manage_document' => 'Tüm belgelerinizi yönetin',
    'share' => 'Paylaş',
    'share_document' => 'Belgeleri paylaş',
    'user' => 'Kullanıcı',
    'role' => 'Rol',
    'shared_by' => 'Tarafından paylaşıldı :',
    'todo' => 'Yapmak',
    'todo_list' => 'Yapılacaklar listesi',
    'task' => 'Görev',
    'date' => 'Tarih',
    'no_task' => 'Senin görevin yok!',
    'cancel' => 'İptal etmek',
    'memos' => 'Notlar',
    'manage_memos' => 'Tüm notlarınızı yönetin',
    'all_memos' => 'Tüm notlar',
    'share_memos' => 'Not paylaş',
    'view' => 'Görünüm',
    'heading' => 'Başlık',
    'reminders' => 'Hatırlatmalar',
    'event_name' => 'Etkinlik ismi',
    'time' => 'Zaman',
    'repeat' => 'Tekrar et',
    'one_time' => 'Bir kere',
    'every_day' => 'Her gün',
    'every_week' => 'Her hafta',
    'every_month' => 'Her ay',
    'choose_anyone' => 'Lütfen birini seç',
    'add_reminder' => 'Hatırlatma ekle',
    'reminder_details' => 'Hatırlatma Ayrıntıları',
    'delete_reminder' => 'Hatırlatıcıyı sil',
    'change_reminder_repeat' => 'Hatırlatma tekrarını değiştir',
    'uploaded_date' => 'Yükleme Tarihi',
    'previous_date' => 'Önceki Tarih',
    'next_date' => 'Gelecek Tarih',
    'document_share_notification' => '<i>:shared_by </i> bir belge paylaştı - <i>:document_name</i>',
    'memo' => 'Memo',
    'messages' => 'Mesajlar',
    'type_message' => 'Mesaj yazın ...',
    'create_message' => 'Mesaj oluşturmak',
    'view_message' => 'Mesajı görüntüle',
    'essentials_module' => 'Temel Modül',
    'new_message_notification' => 'Gönderenden yeni mesajlarınız var',
    'hrm' => 'HRM',
    'leave_type' => 'Bırakma Türü',
    'all_leave_types' => 'Tüm izin türleri',
    'max_leave_count' => 'Maksimum İzin Sayısı',
    'add_leave_type' => 'İzin Türü Ekle',
    'edit_leave_type' => 'İzin Türünü Düzenle',
    'leave' => 'Ayrılmak',
    'all_leaves' => 'Tüm Yapraklar',
    'end_date' => 'Bitiş tarihi',
    'start_date' => 'Başlangıç tarihi',
    'reason' => 'Akıl',
    'add_leave' => 'İzin Ekle',
    'approved' => 'Onaylandı',
    'change_status' => 'Durum değiştirmek',
    'cancelled' => 'İptal edildi',
    'employee' => 'Çalışan',
    'upcoming' => 'Yaklaşan',
    'past' => 'Geçmiş',
    'now' => 'Şimdi',
    'new_leave_notification' => ':employee izin başvurusunda bulundu. Referans No - <i>: ref_no </i>',
    'status_change_notification' => ':admin :status belirtin. Referans No - <i>:ref_no </i>',
    'essentials_settings' => 'Temel Ayarlar',
    'leave_ref_no_prefix' => 'Referans No. önekinden ayrıl',
    'leave_instructions' => 'Talimatları Bırak',
    'attendance' => 'Seyirci',
    'all_attendance' => 'Tüm Katılım',
    'add_attendance' => 'Katılım Ekle',
    'clock_in_time' => 'Zamanda saat',
    'clock_out_time' => 'Saatten çıkış zamanı',
    'employees' => 'Çalışanlar',
    'ip_address' => 'IP adresi',
    'clock_in_note' => 'Notta saat',
    'clock_out_note' => 'Saat notu',
    'clock_in' => 'İş başı yapmak',
    'clock_out' => 'Saat Dışı',
    'clock_in_success' => 'Başarıyla Takıldı',
    'clock_out_success' => 'Başarıyla Tıklandı',
    'not_allowed' => 'İzin verilmedi',
    'edit_attendance' => 'Katılımı Düzenle',
    'clocked_in_at' => 'Şuraya yerleştirildi',
    'clock_in_clock_out' => 'Saat Girişi - Saat Dışı',
    'activity' => 'Aktivite',
    'status_changed_to' => 'Durum şu şekilde değiştirildi: durum',
    'leaves_summary_for_user' => 'Kullanıcı için Özet: Kullanıcı',
    'total_work_hours' => 'Toplam çalışma saatleri',
    'leave_count_interval' => 'Sayım aralığını bırak',
    'current_month' => 'İçinde bulunduğumuz ay',
    'current_fy' => 'Cari mali yıl',
    'remaining' => 'Kalan',
    'max_allowed_leaves' => 'İzin verilen maksimum izin',
    'within_current_month' => 'Bu ay içinde',
    'within_current_fy' => 'Cari mali yıl içinde',
    'payroll' => 'Maaş bordrosu',
    'all_payrolls' => 'Tüm Bordrolar',
    'proceed' => 'İlerlemek',
    'month_year' => 'Ay yıl',
    'add_payroll' => 'Bordro Ekle',
    'edit_payroll' => 'Bordro Düzenle',
    'payroll_of_employee' => '<strong>:date</strong> için <strong>:employee</strong> bordrosu',
    'total_work_duration' => 'Toplam çalışma süresi',
    'amount_per_unit_duartion' => 'Birim süre başına tutar',
    'allowances' => 'Kazanç',
    'allowance' => 'Kazanç',
    'deductions' => 'Kesintiler',
    'deduction' => 'Kesintisi',
    'duration_unit' => 'Süre Birimi',
    'gross_amount' => 'Brüt miktar',
    'payroll_ref_no_prefix' => 'Bordro Referans No. öneki',
    'payroll_added_notification' => 'İçin bordro :month_year ekleyen :created_by. Referans No. :ref_no',
    'payroll_updated_notification' => 'Bordro :month_year revize edilen :created_by. Referans No. :ref_no',
    'holiday' => 'Tatil',
    'all_holidays' => 'Tüm Tatiller',
    'add_holiday' => 'Tatil Ekle',
    'edit_holiday' => 'Tatil Düzenle',
    'payroll_for' => 'Için Bordro',
    'total_payroll' => 'Toplam Bordro',
    'approve_leave' => 'İzni Onayla',
    'amount_type' => 'Tutar Türü',
    'applicable_date' => 'Geçerli Tarih',
    'applicable_date_help' => 'Seçilen tarihe göre maaş bordrosuna gösterilir. Hiçbir tarih seçilmezse, oluşturulan tüm yeni bordroda gösterilir. Bu değeri bordroda değiştirebilirsiniz.',
    'assigned_to' => 'Atandı',
    'estimated_hours' => 'Tahmini Saatler',
    'add_to_do' => 'Yapılacaklar',
    'edit_to_do' => 'Yapılacaklar İçin Düzenle',
    'assign_todos' => 'Başkalarına Yapılacaklar Ata',
    'assigned_by' => 'Atanmak',
    'comments' => 'Yorumlar',
    'add_comment' => 'Yorum ekle',
    'upload' => 'Yükleme',
    'assigned_to' => 'Atandı',
    'mark_as_complted' => 'Tamamlandı olarak işaretle',
    'new' => 'Yeni',
    'in_progress' => 'Devam etmekte',
    'on_hold' => 'Beklemede',
    'low' => 'Düşük',
    'medium' => 'Orta',
    'high' => 'Yüksek',
    'urgent' => 'Acil',
    'priority' => 'Öncelikli',
    'change_status' => 'Durum değiştirmek',
    'task_id' => 'Görev Kimliği',
    'essentials_n_hrm_settings' => 'Temel Bilgiler ve İKY Ayarları',
    'essentials_todos_prefix' => 'Yapılacaklar Kimliği Öneki',
    'new_task_notification' => ':assigned_by sizi yeni bir göreve ekledi <br> Görev Kimliği - :task_id',
    'new_task_comment_notification' => ':added_by göreve yorum yaptı: size atanan :task_id',
    'new_task_document_notification' => ':uploaded_by bir belge yükleyerek yüklendi - :task_id atanan görev kimliği',
    'clock_in_clock_out_validation_msg' => 'Saat giriş ve Saat çıkış süresi, var olan katılımla örtüşemez',
    'departments' => 'Bölümler',
    'manage_departments' => 'Bölümleri Yönet',
    'department_id' => 'Departman Kimliği',
    'department_code_help' => 'Benzersiz departman kimliği girin',
    'designations' => 'Tanımları',
    'manage_designations' => 'Atamaları yönet',
    'designation_code_help' => 'Benzersiz adlandırma kimliği girin',
    'department' => 'Bölüm',
    'designation' => 'Tanım',
    'hrm_details' => 'HRM Ayrıntıları',
    'document_shared' => 'Paylaşılan Belge',
    'leave_status_changed' => 'İzin durumu değişti',
    'leave_added_successfully' => 'Bırakma başarıyla eklendi',
    'new_message' => 'Yeni Mesaj',
    'new_comment' => 'Yeni yorum',
    'new_document' => 'Yeni belge',
    'new_task_added' => 'Yeni görev eklendi',
    'payroll_added' => 'Bordro eklendi',
    'payroll_updated' => 'Bordro güncellendi',
    'shifts' => 'Kaymalar',
    'add_shift' => 'Vardiya Ekle',
    'assign_users' => 'Kullanıcı Ata',
    'grace_before_checkin' => 'Check-in öncesi lütuf',
    'grace_before_checkin_help' => '(dakika olarak) bu süre fazla mesai olarak sayılmayacak',
    'grace_after_checkin' => 'Check-in sonrası lütuf',
    'grace_after_checkin_help' => '(dakika olarak) bu süre geç sayılmaz',
    'grace_before_checkout' => 'Ödeme yapmadan önce zarafet',
    'grace_before_checkout_help' => '(dakika olarak) bu süre erken sol sayılmaz',
    'grace_after_checkout' => 'Ödeme yapıldıktan sonra zarafet',
    'grace_time' => 'Zarafet zamanı',
    'shift_not_allocated' => 'Şu an için aktif vardiya yok',
    'shift_not_over' => 'Shift bitmedi',
    'attendance_by_shift' => 'Vardiya ile devam',
    'attendance_by_date' => 'Tarihe göre katılım',
    'present' => 'Mevcut',
    'absent' => 'Yok',
    'shift' => 'Vardiya',
    'no_data_found' => 'Veri bulunamadı',
    'import_attendance' => 'İthalat Katılımı',
    'email_ins' => 'Kullanıcının e-posta kimliği',
    'clock_in_time_ins' => "'Y-m-d H:i:s' formatında saat cinsinden saat,",
    'clock_out_time_ins' => "'Y-m-d H:i:s formatında saat aşımı süresi",
    'add_latest_attendance' => 'Son katılım ekle',
    'select_employee' => 'Çalışan seçin',
    'shift_type' => 'Vites Tipi',
    'fixed_shift' => 'Sabit vites değiştirme',
    'flexible_shift' => 'Esnek geçiş',
    'work_duration' => 'Çalışma Süresi',
    'shift_type_tooltip' => '<b> Sabit Geçiş: </b> Grace Saat giriş ve Saat çıkış zamanı, zarafet zamanı ile düzeltildi. <br> <b> Esnek Geçiş: </b> Saat giriş ve Saat çıkış zamanı yok.',
    'shift_datatable_tooltip' => 'Vardiyaları yönetin ve kullanıcılara vardiya atayın',
    'clocked_in' => 'Saatte ',
    'upcoming_holidays' => 'Yaklaşan tatiller',
    'leaves' => 'Ayrılıyor',
    'my_leaves' => 'Yapraklarım',
    'holidays' => 'Tatiller',
    'birthdays' => 'doğum günleri',
    'todays_attendance' => 'Bugün Katılım',
    'already_clocked_in' => 'Saati zaten almış',
    'not_clocked_in' => 'Saatli değil',
    'crud_leave_type' => 'İzin türü Ekle / Düzenle / Görüntüle / Sil',
    'crud_all_leave' => 'Tüm izinleri Ekle / Düzenle / Görüntüle / Sil',
    'crud_own_leave' => 'Kendi iznini ekle / görüntüle',
    'crud_all_attendance' => 'Tüm katılımı Ekle / Düzenle / Görüntüle / Sil',
    'view_own_attendance' => 'Kendi katılımınızı görüntüleyin',
    'crud_department' => 'Departman Ekle / Düzenle / Görüntüle / Sil',
    'crud_designation' => 'Atama Ekle / Düzenle / Görüntüle / Sil',
    'knowledge_base' => 'Bilgi tabanı',
    'content' => 'İçerik',
    'title' => 'Başlık',
    'add_knowledge_base' => 'Bilgi tabanı ekle',
    'add_section' => 'Bölüm Ekle',
    'add_article' => 'Makale Ekle',
    'private' => 'Özel',
    'public' => 'Halka açık',
    'share_with' => 'İle paylaş',
    'share_only_with' => 'Yalnızca paylaş',
    'only_with' => 'Yalnızca birlikte',
    'edit_knowledge_base' => 'Bilgi tabanını düzenle',
    'edit_section' => 'Bölümü düzenle',
    'edit_article' => 'Makaleyi düzenle',
    'docs' => 'Dokümanlar',
    'view_shared_docs' => 'Paylaşılan belgeleri görüntüleyin',
    'spreadsheets' => 'Elektronik Tablolar',
    'no_docs_found' => 'Belge bulunamadı!',
    'timing' => 'Zamanlama',
    'your_shifts' => 'Vardiyalarınız',
    'clock_in_location' => 'Konumdaki saat',
    'clock_out_location' => 'Saat çıkışı konumu',
    'is_location_required' => 'Konum gerekli mi?',
    'you_must_enable_location' => 'Konum gereklidir, konumu etkinleştirmelisiniz',
    'allow_location' => 'Konuma izin ver',
    'salary' => 'Temel maaş',
    'pay_cycle' => 'Ödeme Döngüsü',
    'week' => 'Hafta',
    'per' => 'Başına',
    'pay_components' => 'Ödeme Bileşenleri',
    'add_pay_component' => 'Ödeme Bileşeni Ekle',
    'edit_pay_component' => 'Ödeme Bileşenini Düzenle',
    'view_pay_component' => 'Ödeme Bileşenini Görüntüle',
    'rate' => 'Puan',
    'total_earnings' => 'Toplam kazanç',
    'total_deductions' => 'Toplam kesintiler',
    'net_pay' => 'Net ödeme',
    'payslip_for_the_month' => 'Ay: ay için maaş bordrosu :month :year',
    'in_words' => 'Kelimelerle',
    'days_present' => 'Günler mevcut',
    'days_absent' => 'Olmayan günler',
    'sale_commission' => 'Satış Komisyonu',
    'payroll_already_added_for_given_user' => 'Belirli kullanıcılar için bordro zaten eklendi',
    'notify_employee' => 'Bildirim Gönder',
    'payroll_for_month' => '<strong>:date </strong> için maaş bordrosu',
    'payroll_group_name' => 'Bordro grubu adı',
    'all_payroll_groups' => 'Tüm bordro grupları',
    'view_payroll_group' => 'Bordro grubunu görüntüle',
    'payroll_group' => 'Bordro grubu',
    'add_payment_for_payroll_group' => 'Bordro grubu için ödeme ekle',
    'group_status_tooltip' => 'Durum <code> nihai </code> ise, o zaman <code> ödeme eklenebilir </code> aksi takdirde eklenemez',
    'total_gross_amount' => 'Toplam brüt tutar',
    'allow_users_for_attendance_moved_to_role' => 'Kullanıcıların kendi katılımlarını girmelerine izin ver ayarı role taşındı.',
    'allow_users_for_attendance_from_web' => "Kullanıcıların web'den kendi katılımlarını girmelerine izin ver",
    'allow_users_for_attendance_from_api' => "Kullanıcıların api'den kendi katılımlarını girmelerine izin ver",
    'allow_auto_clockout' => 'Otomatik zaman aşımı yap',
    'allow_auto_clockout_tooltip' => 'Kullanıcı, belirli bir süre sonra otomatik olarak saat aşımına uğrayacak',
    'auto_clockout_time' => 'Otomatik saat aşımı süresi',
    'work_duration_hour' => ':duration saat',
    'total_leaves_days' => ':total_leaves gün',
    'attendance_report' => 'Rapport de présence',
    'is_present' => 'Est Présent',
    'days_present' => 'Jours Présent',
    'days_absent' => 'Jours Absent',
    'attendance_report_for' => 'Rapport de présence pour',
];
