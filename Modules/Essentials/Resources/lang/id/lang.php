<?php

return[
    'essentials' => 'Essentials',
    'document' => 'Dokumen',
    'description' => '<PERSON><PERSON><PERSON><PERSON>',
    'submit' => 'Kirimkan',
    'delete' => 'Menghapus',
    'download' => 'Unduh',
    'name' => 'Nama',
    'created_at' => 'Tanggal Dibuat',
    'action' => 'Tindakan',
    'all_documents' => 'Semua dokumen',
    'manage_document' => 'Kelola semua dokumen Anda',
    'share' => 'Bagikan',
    'share_document' => 'Bagikan dokumen',
    'user' => 'Pengguna',
    'role' => 'Wewenang',
    'shared_by' => 'Dibagikan oleh :',
    'todo' => 'Melakukan',
    'todo_list' => 'Daftar Kerja',
    'task' => 'Tugas',
    'date' => 'Tanggal',
    'no_task' => 'Kamu tidak punya tugas!',
    'cancel' => 'Membatalkan',
    'memos' => 'Memo',
    'manage_memos' => '<PERSON><PERSON>la semua memo Anda',
    'all_memos' => 'Semua memo',
    'share_memos' => 'Bagikan memo',
    'view' => 'Melihat',
    'heading' => 'Menuju',
    'reminders' => 'Pengingat',
    'event_name' => 'Nama Acara',
    'time' => 'Waktu',
    'repeat' => 'Ulang',
    'one_time' => 'Satu kali',
    'every_day' => 'Setiap hari',
    'every_week' => 'Setiap minggu',
    'every_month' => 'Setiap bulan',
    'choose_anyone' => 'Silakan pilih siapa saja',
    'add_reminder' => 'Tambahkan pengingat',
    'reminder_details' => 'Detail Pengingat',
    'delete_reminder' => 'Hapus pengingat',
    'change_reminder_repeat' => 'Ubah pengingat ulangi',
    'uploaded_date' => 'Tanggal Diunggah',
    'previous_date' => 'Tanggal Sebelumnya',
    'next_date' => 'Tanggal Berikutnya',
    'document_share_notification' => '<i>:shared_by </i> membagikan dokumen - <i>:document_name </i>',
    'memo' => 'Memo',
    'messages' => 'Pesan',
    'type_message' => 'Ketikkan pesan ...',
    'create_message' => 'Menulis pesan',
    'view_message' => 'Lihat Pesan',
    'essentials_module' => 'Modul Essentials',
    'new_message_notification' => 'Anda memiliki pesan baru dari: pengirim',
    'hrm' => 'HRM',
    'leave_type' => 'Tipe Cuti',
    'all_leave_types' => 'Semua jenis cuti',
    'max_leave_count' => 'Hitungan Cuti Maks',
    'add_leave_type' => 'Tambahkan Tipe Cuti',
    'edit_leave_type' => 'Edit Jenis Cuti',
    'leave' => 'Meninggalkan',
    'all_leaves' => 'Semua Daun',
    'end_date' => 'Tanggal Akhir',
    'start_date' => 'Mulai tanggal',
    'reason' => 'Alasan',
    'add_leave' => 'Tambah Cuti',
    'approved' => 'Disetujui',
    'change_status' => 'Merubah status',
    'cancelled' => 'Dibatalkan',
    'employee' => 'Karyawan',
    'upcoming' => 'Mendatang',
    'past' => 'Lalu',
    'now' => 'Sekarang',
    'new_leave_notification' => ':employee melamar cuti. No Referensi - <i>:ref_no </i>',
    'status_change_notification' => ':admin :status cuti Anda. No Referensi - <i>:ref_no </i>',
    'essentials_settings' => 'Pengaturan Essentials',
    'leave_ref_no_prefix' => 'Tinggalkan awalan No. Referensi',
    'leave_instructions' => 'Tinggalkan Instruksi',
    'attendance' => 'Kehadiran',
    'all_attendance' => 'Semua Kehadiran',
    'add_attendance' => 'Tambahkan Kehadiran',
    'clock_in_time' => 'Jam dalam waktu',
    'clock_out_time' => 'Waktu habis',
    'employees' => 'Para karyawan',
    'ip_address' => 'Alamat IP',
    'clock_in_note' => 'Jam dalam catatan',
    'clock_out_note' => 'Clock out note',
    'clock_in' => 'Clock In',
    'clock_out' => 'Clock Out',
    'clock_in_success' => 'Clocked In berhasil',
    'clock_out_success' => 'Clocked Out berhasil',
    'not_allowed' => 'Tidak diizinkan',
    'edit_attendance' => 'Edit Kehadiran',
    'clocked_in_at' => 'Clocked in at',
    'clock_in_clock_out' => 'Jam Masuk - Jam Keluar',
    'activity' => 'Aktivitas',
    'status_changed_to' => 'Status berubah menjadi: status',
    'leaves_summary_for_user' => 'Ringkasan Daun untuk: pengguna',
    'total_work_hours' => 'Total jam kerja',
    'leave_count_interval' => 'Tinggalkan interval hitung',
    'current_month' => 'Bulan berjalan',
    'current_fy' => 'Tahun keuangan saat ini',
    'remaining' => 'Tersisa',
    'max_allowed_leaves' => 'Daun maksimum yang diizinkan',
    'within_current_month' => 'Dalam bulan ini',
    'within_current_fy' => 'Dalam tahun keuangan saat ini',
    'payroll' => 'Daftar gaji',
    'all_payrolls' => 'Semua Penggajian',
    'proceed' => 'Memproses',
    'month_year' => 'Bulan tahun',
    'add_payroll' => 'Tambahkan Gaji',
    'edit_payroll' => 'Edit Penggajian',
    'payroll_of_employee' => 'Daftar gaji <strong>:employee </strong> untuk <strong>:date </strong>',
    'total_work_duration' => 'Total durasi kerja',
    'amount_per_unit_duartion' => 'Jumlah per durasi unit',
    'allowances' => 'Pendapatan',
    'allowance' => 'Produktif',
    'deductions' => 'Pengurangan',
    'deduction' => 'Deduksi',
    'duration_unit' => 'Unit Durasi',
    'gross_amount' => 'Jumlah bruto',
    'payroll_ref_no_prefix' => 'Awalan No. Referensi Penggajian',
    'payroll_added_notification' => 'Penggajian untuk :month_year ditambahkan oleh :created_by. No. Referensi :ref_no',
    'payroll_updated_notification' => 'Daftar gaji untuk :month_year direvisi oleh :created_by. No. Referensi :ref_no',
    'holiday' => 'Liburan',
    'all_holidays' => 'Semua Liburan',
    'add_holiday' => 'Tambah Liburan',
    'edit_holiday' => 'Edit Liburan',
    'payroll_for' => 'Payroll For',
    'total_payroll' => 'Total Penggajian',
    'approve_leave' => 'Setujui Cuti',
    'amount_type' => 'Jumlah Jenis',
    'applicable_date' => 'Tanggal Berlaku',
    'applicable_date_help' => 'Berdasarkan tanggal yang dipilih itu akan ditampilkan ke daftar gaji. Jika tidak ada tanggal yang dipilih maka akan ditampilkan di semua daftar gaji baru yang dibuat. Anda dapat mengubah nilai ini dalam daftar gaji.',
    'assigned_to' => 'Ditugaskan untuk',
    'estimated_hours' => 'Perkiraan Jam',
    'add_to_do' => 'Tambah Aktivitas',
    'edit_to_do' => 'Edit Aktivitas',
    'assign_todos' => 'Tetapkan Untuk Dilakukan untuk orang lain',
    'assigned_by' => 'Ditanda tangani oleh',
    'comments' => 'Komentar',
    'add_comment' => 'Tambahkan komentar',
    'upload' => 'Unggah',
    'mark_as_complted' => 'Tandai selesai',
    'new' => 'Baru',
    'in_progress' => 'Sedang berlangsung',
    'on_hold' => 'Tertahan',
    'low' => 'Rendah',
    'medium' => 'Medium',
    'high' => 'Tinggi',
    'urgent' => 'Mendesak',
    'priority' => 'Prioritas',
    'change_status' => 'Merubah status',
    'task_id' => 'Id Tugas',
    'essentials_n_hrm_settings' => 'Pengaturan Essentials dan HRM',
    'essentials_todos_prefix' => 'Todos ID Prefix',
    'new_task_notification' => ':assigned_by menambahkan Anda ke tugas baru <br> ID Tugas - :task_id',
    'new_task_comment_notification' => ':added_by mengomentari tugas - :task_id yang ditugaskan untuk Anda',
    'new_task_document_notification' => ':uploaded_by unggah dokumen baru ke tugas - :task_id yang ditugaskan untuk Anda',
    'clock_in_clock_out_validation_msg' => 'Jam masuk dan Jam habis tidak bisa tumpang tindih dengan kehadiran yang ada',
    'departments' => 'Departemen',
    'manage_departments' => 'Kelola Departemen',
    'department_id' => 'ID Departemen',
    'department_code_help' => 'Masukkan ID departemen unik',
    'designations' => 'Penunjukan',
    'manage_designations' => 'Kelola penunjukan',
    'designation_code_help' => 'Masukkan ID penunjukan unik',
    'department' => 'Departemen',
    'designation' => 'Penunjukan',
    'hrm_details' => 'Detail HRM',
    'document_shared' => 'Dokumen Dibagikan',
    'leave_status_changed' => 'Tinggalkan status berubah',
    'leave_added_successfully' => 'Biarkan berhasil ditambahkan',
    'new_message' => 'Pesan Baru',
    'new_comment' => 'Komentar baru',
    'new_document' => 'Dokumen baru',
    'new_task_added' => 'Tugas baru ditambahkan',
    'payroll_added' => 'Gaji ditambahkan',
    'payroll_updated' => 'Daftar gaji diperbarui',
    'shifts' => 'Bergeser',
    'add_shift' => 'Tambah Shift',
    'assign_users' => 'Tetapkan Pengguna',
    'grace_before_checkin' => 'Rahmat sebelum checkin',
    'grace_before_checkin_help' => '(dalam menit) saat ini tidak akan dihitung sebagai lembur',
    'grace_after_checkin' => 'Rahmat setelah checkin',
    'grace_after_checkin_help' => '(dalam menit) saat ini tidak akan dihitung sebagai terlambat',
    'grace_before_checkout' => 'Rahmat sebelum checkout',
    'grace_before_checkout_help' => '(dalam menit) saat ini tidak akan dihitung sebagai kiri awal',
    'grace_after_checkout' => 'Rahmat setelah checkout',
    'grace_time' => 'Grace Time',
    'shift_not_allocated' => 'Tidak ada shift aktif untuk saat ini',
    'shift_not_over' => 'Shift not over',
    'attendance_by_shift' => 'Kehadiran secara bergiliran',
    'attendance_by_date' => 'Kehadiran berdasarkan tanggal',
    'present' => 'Menyajikan',
    'absent' => 'Tidak hadir',
    'shift' => 'Bergeser',
    'no_data_found' => 'Tidak ada data ditemukan',
    'import_attendance' => 'Impor Kehadiran',
    'email_ins' => 'Id email pengguna',
    'clock_in_time_ins' => "Jam dalam waktu dalam format 'Y-m-d H:i:s'",
    'clock_out_time_ins' => "Waktu habis dalam format 'Y-m-d H:i:s",
    'add_latest_attendance' => 'Tambahkan kehadiran terbaru',
    'select_employee' => 'Pilih karyawan',
    'shift_type' => 'Jenis Pergeseran',
    'fixed_shift' => 'Memperbaiki shift',
    'flexible_shift' => 'Pergeseran fleksibel',
    'work_duration' => 'Lama pengerjaan',
    'shift_type_tooltip' => '<b> Shift Tetap: </b> Waktu Tetap Masuk & Jam habis dengan waktu tambahan. <br> <b> Pergeseran Fleksibel: </b> Tidak ada waktu Jam masuk & Jam habis.',
    'shift_datatable_tooltip' => 'Kelola shift dan tetapkan shift ke pengguna',
    'clocked_in' => 'Masuk',
    'upcoming_holidays' => 'Liburan yang akan datang',
    'leaves' => 'Berangkat',
    'my_leaves' => 'Daun saya',
    'holidays' => 'Liburan',
    'birthdays' => 'ulang tahun',
    'todays_attendance' => 'Hari ini Kehadiran',
    'already_clocked_in' => 'Sudah masuk',
    'not_clocked_in' => 'Tidak masuk',
    'crud_leave_type' => 'Tambah / Edit / Lihat / Hapus jenis cuti',
    'crud_all_leave' => 'Tambah / Edit / Lihat / Hapus semua cuti',
    'crud_own_leave' => 'Tambahkan / Lihat cuti sendiri',
    'crud_all_attendance' => 'Tambah / Edit / Lihat / Hapus semua kehadiran',
    'view_own_attendance' => 'Lihat kehadiran sendiri',
    'crud_department' => 'Tambah / Edit / Lihat / Hapus departemen',
    'crud_designation' => 'Tambah / Edit / Lihat / Hapus penunjukan',
    'knowledge_base' => 'Dasar pengetahuan',
    'content' => 'Kandungan',
    'title' => 'Judul',
    'add_knowledge_base' => 'Tambahkan basis pengetahuan',
    'add_section' => 'Tambahkan Bagian',
    'add_article' => 'Tambahkan Artikel',
    'private' => 'Pribadi',
    'public' => 'Publik',
    'share_with' => 'Berbagi dengan',
    'share_only_with' => 'Hanya berbagi dengan',
    'only_with' => 'Hanya dengan',
    'edit_knowledge_base' => 'Edit basis pengetahuan',
    'edit_section' => 'Edit bagian',
    'edit_article' => 'Edit artikel',
    'docs' => 'Dokumen',
    'view_shared_docs' => 'Lihat dokumen bersama',
    'spreadsheets' => 'Spreadsheets',
    'no_docs_found' => 'Tidak ada dokumen yang ditemukan!',
    'timing' => 'Pengaturan waktu',
    'your_shifts' => 'Giliranmu',
    'clock_in_location' => 'Jam di lokasi',
    'clock_out_location' => 'Jam keluar lokasi',
    'is_location_required' => 'Apakah lokasi diperlukan?',
    'you_must_enable_location' => 'Lokasi diperlukan, Anda harus mengaktifkan lokasi',
    'allow_location' => 'Izinkan lokasi',
    'salary' => 'Gaji pokok',
    'pay_cycle' => 'Pay Cycle',
    'week' => 'Minggu',
    'per' => 'Per',
    'pay_components' => 'Komponen Pembayaran',
    'add_pay_component' => 'Tambahkan Komponen Pembayaran',
    'edit_pay_component' => 'Edit Komponen Pembayaran',
    'view_pay_component' => 'Lihat Komponen Gaji',
    'rate' => 'Menilai',
    'total_earnings' => 'Penghasilan total',
    'total_deductions' => 'Potongan total',
    'net_pay' => 'Gaji bersih',
    'payslip_for_the_month' => 'Slip gaji untuk :month :year',
    'in_words' => 'Dalam kata kata',
    'days_present' => 'Hari-hari sekarang',
    'days_absent' => 'Hari tidak ada',
    'sale_commission' => 'Komisi Penjualan',
    'payroll_already_added_for_given_user' => 'Penggajian telah ditambahkan, untuk pengguna tertentu',
    'notify_employee' => 'Kirim Pemberitahuan',
    'payroll_for_month' => 'Gaji untuk <strong>:date </strong>',
    'payroll_group_name' => 'Nama grup penggajian',
    'all_payroll_groups' => 'Semua grup penggajian',
    'view_payroll_group' => 'Lihat grup penggajian',
    'payroll_group' => 'Grup penggajian',
    'add_payment_for_payroll_group' => 'Tambahkan pembayaran untuk grup penggajian',
    'group_status_tooltip' => 'Jika statusnya <code> final </code> maka <code> pembayaran bisa ditambahkan </code> jika tidak',
    'total_gross_amount' => 'Jumlah kotor total',
    'allow_users_for_attendance_moved_to_role' => 'Setelan Izinkan pengguna memasukkan kehadirannya sendiri telah dipindahkan ke peran.',
    'allow_users_for_attendance_from_web' => 'Izinkan pengguna memasukkan kehadiran mereka sendiri dari web',
    'allow_users_for_attendance_from_api' => 'Izinkan pengguna untuk memasukkan kehadiran mereka sendiri dari api',
    'allow_auto_clockout' => 'Lakukan jam otomatis',
    'allow_auto_clockout_tooltip' => 'Pengguna akan keluar secara otomatis setelah waktu tertentu',
    'auto_clockout_time' => 'Waktu keluar otomatis',
    'work_duration_hour' => ':duration jam',
    'total_leaves_days' => ':total_leaves days',
    'attendance_report' => 'Laporan Kehadiran',
    'is_present' => 'Hadirlah',
    'days_present' => 'Hari Hadir',
    'days_absent' => 'Hari Absen',
    'attendance_report_for' => 'Laporan Kehadiran untuk',
];
