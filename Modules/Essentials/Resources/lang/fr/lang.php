<?php

return [
    'essentials' => 'Essentiel',
    'document' => 'Document',
    'description' => 'La description',
    'submit' => 'Soumettre',
    'delete' => 'Effacer',
    'download' => 'Télécharger',
    'name' => 'Prénom',
    'created_at' => 'Date de création',
    'action' => 'Action',
    'all_documents' => 'Tous les documents',
    'manage_document' => 'Gérer tous vos documents',
    'share' => 'Partager',
    'share_document' => 'Partager des documents',
    'user' => 'Utilisateur',
    'role' => 'Rôle',
    'shared_by' => 'Partagé par :',
    'todo' => 'Faire',
    'todo_list' => 'Liste de choses à faire',
    'task' => 'Tâche',
    'date' => 'Rendez-vous amoureux',
    'no_task' => "Vous n'avez aucune tâche!",
    'cancel' => 'Annuler',
    'memos' => 'Mémos',
    'name' => 'Prénom',
    'manage_memos' => 'Gérer tous vos mémos',
    'all_memos' => 'Tous les mémos',
    'share_memos' => 'Partager des mémos',
    'view' => 'Vue',
    'heading' => 'Titre',
    'reminders' => 'Rappels',
    'event_name' => "Nom de l'événement",
    'time' => 'Temps',
    'repeat' => 'Répéter',
    'one_time' => 'Une fois',
    'every_day' => 'Tous les jours',
    'every_week' => 'Toutes les semaines',
    'every_month' => 'Chaque mois',
    'choose_anyone' => "S'il vous plaît choisir quelqu'un",
    'add_reminder' => 'Ajouter un rappel',
    'reminder_details' => 'Détails du rappel',
    'delete_reminder' => 'Supprimer le rappel',
    'change_reminder_repeat' => 'Changer le rappel de répétition',
    'uploaded_date' => 'Date de téléchargement',
    'previous_date' => 'Date précédente',
    'next_date' => 'Date suivante',
    'new_message_notification' => 'Vous avez de nouveaux messages de :sender',
    'hrm' => 'GRH',
    'leave_type' => 'Type de congé',
    'all_leave_types' => 'Tous les types de congés',
    'max_leave_count' => 'Max Leave Count',
    'add_leave_type' => 'Ajouter un type de congé',
    'edit_leave_type' => 'Modifier le type de congé',
    'leave' => 'Laisser',
    'all_leaves' => 'Toutes les feuilles',
    'end_date' => 'Date de fin',
    'start_date' => 'Date de début',
    'reason' => 'Raison',
    'add_leave' => 'Ajouter congé',
    'approved' => 'Approuvé',
    'change_status' => 'Changer le statut',
    'cancelled' => 'Annulé',
    'employee' => 'Employé',
    'upcoming' => 'Prochain',
    'past' => 'Passé',
    'now' => 'À présent',
    'new_leave_notification' => ':employee a demandé un congé. Numéro de référence - <i>:ref_no </i>',
    'status_change_notification' => ":admin: l'état de votre congé. Numéro de référence - <i>:ref_no </i>",
    'essentials_settings' => 'Paramètres essentiels',
    'leave_ref_no_prefix' => 'Laisser le préfixe du numéro de référence',
    'leave_instructions' => 'Instructions de départ',
    'attendance' => 'Présence',
    'all_attendance' => 'Tous les assistants',
    'add_attendance' => 'Ajouter une présence',
    'clock_in_time' => 'Horloge dans le temps',
    'clock_out_time' => 'Heure de sortie',
    'employees' => 'Employés',
    'ip_address' => 'Adresse IP',
    'clock_in_note' => 'Horloge en note',
    'clock_out_note' => 'Note de sortie',
    'clock_in' => 'Horloge dans',
    'clock_out' => "Sortie d'horloge",
    'clock_in_success' => 'Clocked In avec succès',
    'clock_out_success' => 'Clocked Out avec succès',
    'not_allowed' => 'Interdit',
    'edit_attendance' => "Modifier l'assistance",
    'clocked_in_at' => 'Enregistré à',
    'clock_in_clock_out' => 'Clock In - Clock Out',
    'profile_info' => 'Informations sur le profil',
    'dob' => 'Date de naissance',
    'married' => 'Marié',
    'unmarried' => 'Célibataire',
    'marital_status' => 'État civil',
    'blood_group' => 'Groupe sanguin',
    'divorced' => 'Divorcé',
    'contact_no' => 'Numéro de contact',
    'fb_link' => 'Lien Facebook',
    'twitter_link' => 'Lien Twitter',
    'social_media' => 'Médias sociaux :number',
    'permanent_address' => 'Adresse permanente',
    'current_address' => 'Adresse actuelle',
    'guardian_name' => 'Nom du gardien',
    'custom_field' => 'Champ personnalisé :number',
    'bank_details' => 'Coordonnées bancaires',
    'account_holder_name' => 'Nom du titulaire du compte',
    'account_number' => 'Numéro de compte',
    'bank_name' => 'Nom de banque',
    'bank_code' => "Code d'identifiant bancaire",
    'bank_code_help' => 'Un code unique pour identifier la banque dans votre pays, par exemple: code IFSC',
    'branch' => 'Branche',
    'tax_payer_id' => 'Contribuable ID',
    'tax_payer_id_help' => "Numéro d'identification fiscale de l'employé, par exemple, la carte PAN en Inde",
    'more_info' => "Plus d'information",
    'hrm_info' => 'Informations GRH',
    'activity' => 'Activité',
    'status_changed_to' => 'Statut changé à :status',
    'leaves_summary_for_user' => 'Résumé des feuilles pour :user',
    'total_work_hours' => 'Total heures de travail',
    'leave_count_interval' => 'Laisser le compte intervalle',
    'current_month' => 'Mois en cours',
    'current_fy' => 'Exercice en cours',
    'remaining' => 'Restant',
    'max_allowed_leaves' => 'Nombre maximal de congés autorisés',
    'within_current_month' => 'Dans le mois en cours',
    'within_current_fy' => "Au cours de l'exercice en cours",
    'payroll' => 'Paie',
    'all_payrolls' => 'Tous les salaires',
    'proceed' => 'Procéder',
    'month_year' => 'Mois année',
    'add_payroll' => 'Ajouter une paie',
    'edit_payroll' => 'Modifier la paie',
    'payroll_of_employee' => 'Paie de <strong>:employee </strong> pour <strong>:date </strong>',
    'total_work_duration' => 'Durée totale du travail',
    'amount_per_unit_duartion' => 'Montant par unité de durée',
    'allowances' => 'Gains',
    'allowance' => 'Revenus',
    'deductions' => 'Déductions',
    'deduction' => 'Déduction',
    'duration_unit' => 'Unité de durée',
    'gross_amount' => 'Montant brut',
    'payroll_ref_no_prefix' => 'Préfixe du numéro de référence de la paie',
    'payroll_added_notification' => 'Paie pour :month_year ajoutée par :created_by. Numéro de référence :ref_no',
    'payroll_updated_notification' => 'Paie pour :month_year révisée par :created_by. Numéro de référence :ref_no',
    'holiday' => 'Vacances',
    'birthdays' => 'anniversaires',
    'all_holidays' => 'Tous les jours fériés',
    'add_holiday' => 'Ajouter des vacances',
    'edit_holiday' => 'Modifier les vacances',
    'payroll_for' => 'Paie pour',
    'total_payroll' => 'Paie totale',
    'approve_leave' => 'Approuver congé',
    'amount_type' => 'Type de montant',
    'applicable_date' => 'Date applicable',
    'applicable_date_help' => "En fonction de la date sélectionnée, la liste de paie apparaîtra à la liste de paie. Si aucune date n'est sélectionnée, la nouvelle liste de paie créée s'affiche. Vous pouvez modifier cette valeur dans la liste de paie.",
    'assigned_to' => 'Assigné à',
    'estimated_hours' => 'Heures estimées',
    'add_to_do' => 'Ajouter à faire',
    'edit_to_do' => 'Modifier pour faire',
    'assign_todos' => "Attribuer à faire à d'autres",
    'assigned_by' => 'Assigné par',
    'comments' => 'Commentaires',
    'add_comment' => 'Ajouter un commentaire',
    'upload' => 'Télécharger',
    'mark_as_complted' => 'Marquer comme terminé',
    'new' => 'Nouveau',
    'in_progress' => 'En cours',
    'on_hold' => 'En attente',
    'low' => 'Faible',
    'medium' => 'Moyen',
    'high' => 'Haute',
    'urgent' => 'Urgent',
    'priority' => 'Priorité',
    'change_status' => 'Changer le statut',
    'task_id' => 'Identifiant de tâche',
    'essentials_n_hrm_settings' => 'Paramètres essentiels et HRM',
    'essentials_todos_prefix' => 'Préfixe ID Todos',
    'new_task_notification' => ':assigned_by vous a ajouté à une nouvelle tâche <br> Task ID - :task_id',
    'new_task_comment_notification' => ':added_by a commenté la tâche - :task_id attribué à vous',
    'new_task_document_notification' => ':uploaded_by a chargé un nouveau document dans la tâche - :task_id attribué à vous',
    'clock_in_clock_out_validation_msg' => "Les heures d'arrivée et de départ ne peuvent pas se chevaucher avec une présence existante",
    'departments' => 'Départements',
    'manage_departments' => 'Gérer les départements',
    'department_id' => 'ID département',
    'department_code_help' => 'Entrez un ID de service unique',
    'designations' => 'Désignations',
    'manage_designations' => 'Gérer les désignations',
    'designation_code_help' => 'Entrez un ID de désignation unique',
    'department' => 'Département',
    'designation' => 'La désignation',
    'hrm_details' => 'Détails HRM',
    'document_shared' => 'Document partagé',
    'leave_status_changed' => 'Statut de congé modifié',
    'leave_added_successfully' => 'Laisser ajouté avec succès',
    'new_message' => 'Nouveau Messsage',
    'new_comment' => 'Nouveau commentaire',
    'new_document' => 'Nouveau document',
    'new_task_added' => 'Nouvelle tâche ajoutée',
    'payroll_added' => 'Paie ajoutée',
    'payroll_updated' => 'Mise à jour de la paie',
    'shifts' => 'Shifts',
    'add_shift' => 'Ajouter un décalage',
    'assign_users' => 'Attribuer des utilisateurs',
    'grace_before_checkin' => "Grace avant l'enregistrement",
    'grace_before_checkin_help' => '(en minute) ce temps ne comptera pas comme heures supplémentaires',
    'grace_after_checkin' => "Grace après l'enregistrement",
    'grace_after_checkin_help' => '(en minute) cette fois ne comptera pas comme tard',
    'grace_before_checkout' => 'Grace avant le départ',
    'grace_before_checkout_help' => '(en minute) cette fois ne sera pas comptée comme à gauche',
    'grace_after_checkout' => 'Grace après le départ',
    'grace_time' => 'Temps de grâce',
    'shift_not_allocated' => 'Pas de changement actif pour cette fois',
    'shift_not_over' => 'Pas de changement',
    'attendance_by_shift' => 'Fréquentation par quart de travail',
    'attendance_by_date' => 'Fréquentation par date',
    'present' => 'Présent',
    'absent' => 'Absent',
    'shift' => 'Décalage',
    'no_data_found' => 'Aucune donnée disponible',
    'import_attendance' => 'Importation de présence',
    'email_ins' => "Identifiant e-mail de l'utilisateur",
    'clock_in_time_ins' => "Horloge dans le temps au format 'Y-m-d H:i:s'",
    'clock_out_time_ins' => "Temps de sortie au format 'Y-m-d H:i:s",
    'add_latest_attendance' => 'Ajouter la dernière présence',
    'select_employee' => "Sélectionner l'employé",
    'shift_type' => 'Type de décalage',
    'fixed_shift' => 'Changement fixe',
    'flexible_shift' => 'Changement flexible',
    'work_duration' => 'Durée du travail',
    'shift_type_tooltip' => "<b> Horaire fixe: </b> Horloge fixe entrant et sortant avec le temps de grâce. <br> <b> Horaire flexible: </b> Pas d'horloge entrant et sortant.",
    'shift_datatable_tooltip' => "Gérer les équipes et attribuer l'équipe aux utilisateurs",
    'clocked_in' => 'Pointé sur',
    'upcoming_holidays' => 'Prochains jours fériés',
    'leaves' => 'Feuilles',
    'my_leaves' => 'Mes congés',
    'holidays' => 'Vacances',
    'todays_attendance' => "Aujourd'hui Présence",
    'already_clocked_in' => 'Déjà pointé',
    'not_clocked_in' => 'Pas de pointage',
    'crud_leave_type' => 'Ajouter / Modifier / Afficher / Supprimer un type de congé',
    'crud_all_leave' => 'Ajouter / Modifier / Afficher / Supprimer tout congé',
    'crud_own_leave' => 'Ajouter / Afficher votre propre congé',
    'crud_all_attendance' => 'Ajouter / Modifier / Afficher / Supprimer toute présence',
    'view_own_attendance' => 'Afficher sa propre participation',
    'crud_department' => 'Ajouter / Modifier / Afficher / Supprimer un département',
    'crud_designation' => 'Ajouter / Modifier / Afficher / Supprimer une désignation',
    'knowledge_base' => 'Base de connaissances',
    'content' => 'Teneur',
    'title' => 'Titre',
    'add_knowledge_base' => 'Ajouter une base de connaissances',
    'add_section' => 'Ajouter une section',
    'add_article' => 'Ajouter un article',
    'private' => 'Privé',
    'public' => 'Publique',
    'share_with' => 'Partager avec',
    'share_only_with' => 'Partager uniquement avec',
    'only_with' => 'Seulement avec',
    'edit_knowledge_base' => 'Modifier la base de connaissances',
    'edit_section' => 'Modifier la section',
    'edit_article' => "Modifier l'article",
    'docs' => 'Docs',
    'view_shared_docs' => 'Afficher les documents partagés',
    'spreadsheets' => 'Feuilles de calcul',
    'no_docs_found' => 'Aucun document trouvé!',
    'timing' => 'Horaire',
    'your_shifts' => 'Vos changements',
    'clock_in_location' => 'Horloge sur place',
    'clock_out_location' => 'Point de départ',
    'is_location_required' => "L'emplacement est-il requis?",
    'you_must_enable_location' => 'La localisation est obligatoire, vous devez activer la localisation',
    'allow_location' => 'Autoriser la localisation',
    'salary' => 'Salaire de base',
    'pay_cycle' => 'Cycle de paie',
    'week' => 'Semaine',
    'per' => 'Par',
    'pay_components' => 'Composants de paiement',
    'add_pay_component' => 'Ajouter un composant de paye',
    'edit_pay_component' => 'Modifier le composant de paie',
    'view_pay_component' => 'Afficher le composant de paye',
    'rate' => 'Taux',
    'total_earnings' => 'Total des gains',
    'total_deductions' => 'Le total des déductions',
    'net_pay' => 'Salaire net',
    'payslip_for_the_month' => 'Fiche de paie pour le mois de:month :year',
    'in_words' => 'Dans les mots',
    'days_present' => 'Jours présents',
    'days_absent' => "Jours d'absence",
    'sale_commission' => 'Commission de vente',
    'payroll_already_added_for_given_user' => 'La paie a déjà été ajoutée, pour des utilisateurs donnés',
    'notify_employee' => 'Envoyer une notification',
    'payroll_for_month' => 'Paie du <strong>:date </strong>',
    'payroll_group_name' => 'Nom du groupe de paie',
    'all_payroll_groups' => 'Tous les groupes de paie',
    'view_payroll_group' => 'Afficher le groupe de paie',
    'payroll_group' => 'Groupe de paie',
    'add_payment_for_payroll_group' => 'Ajouter un paiement pour le groupe de paie',
    'group_status_tooltip' => 'Si le statut est <code> final </code>, alors le <code> paiement peut être ajouté </code> sinon pas',
    'total_gross_amount' => 'Montant brut total',
    'allow_users_for_attendance_moved_to_role' => 'Le paramètre Autoriser les utilisateurs à entrer leur propre présence a été déplacé vers le rôle. ',
    'allow_users_for_attendance_from_web' => 'Autoriser les utilisateurs à saisir leur propre participation à partir du Web',
    'allow_users_for_attendance_from_api' => "Autoriser les utilisateurs à saisir leur propre présence depuis l'API",
    'allow_auto_clockout' => 'Faire une horloge automatique',
    'allow_auto_clockout_tooltip' => "L'utilisateur sera automatiquement synchronisé après l'heure indiquée",
    'auto_clockout_time' => 'Heure de sortie automatique',
    'work_duration_hour' => ':duration heure',
    'total_leaves_days' => ':total_leaves jours',
    'attendance_report' => 'Rapport de présence',
    'is_present' => 'Est Présent',
    'days_present' => 'Jours Présent',
    'days_absent' => 'Jours Absent',
    'attendance_report_for' => 'Rapport de présence pour',
];
